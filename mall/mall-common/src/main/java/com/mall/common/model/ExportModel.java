package com.mall.common.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Excel导出基础模型
 * 子类可以通过@ExcelProperty注解定义Excel列名
 * 示例:
 * <pre>{@code
 * @Data
 * public class UserExportModel extends ExportModel {
 *     @ExcelProperty("用户ID")
 *     private Long id;
 *     
 *     @ExcelProperty("用户名")
 *     private String username;
 *     
 *     @ExcelProperty("手机号")
 *     private String phone;
 *     
 *     @ExcelIgnore
 *     private String password; // 不导出
 * }
 * }</pre>
 */
@Data
public abstract class ExportModel {
    // 基础属性，如果需要
    @ExcelProperty("创建时间")
    private String createTime;

    @ExcelProperty("更新时间")
    private String updateTime;
} 