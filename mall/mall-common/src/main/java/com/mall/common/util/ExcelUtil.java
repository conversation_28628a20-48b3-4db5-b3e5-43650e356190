package com.mall.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Excel导出工具类
 * 支持从List<Map>、List<POJO>数据导出Excel
 */
@Slf4j
public class ExcelUtil {

    /**
     * 导出Excel到HttpServletResponse
     *
     * @param response    HttpServletResponse
     * @param fileName    文件名
     * @param sheetName   sheet名
     * @param headList    表头列表，如：["用户ID", "用户名", "手机号"]
     * @param keyList     数据的key列表，顺序与表头一致，如：["id", "userName", "phone"]
     * @param dataList    数据列表，List<Map<String, Object>>格式
     */
    public static void exportExcel(HttpServletResponse response, String fileName,
                                   String sheetName, List<String> headList,
                                   List<String> keyList, List<Map<String, Object>> dataList) {
        try {
            // 设置响应头
            setResponseHeader(response, fileName);

            // 转换数据格式
            List<List<Object>> rows = new ArrayList<>();
            if (!CollectionUtils.isEmpty(dataList)) {
                for (Map<String, Object> data : dataList) {
                    List<Object> row = new ArrayList<>();
                    for (String key : keyList) {
                        row.add(data.get(key));
                    }
                    rows.add(row);
                }
            }

            // 构建表头
            List<List<String>> headRows = new ArrayList<>();
            for (String head : headList) {
                headRows.add(Collections.singletonList(head));
            }

            // 获取样式
            HorizontalCellStyleStrategy styleStrategy = getStyleStrategy();

            // 自定义列宽策略
            CustomWidthStyleStrategy customWidthStyleStrategy = new CustomWidthStyleStrategy();

            // 写入数据，添加自适应列宽
            EasyExcel.write(response.getOutputStream())
                    .head(headRows)
                    .registerWriteHandler(styleStrategy)
                    .registerWriteHandler(customWidthStyleStrategy)
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler())
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(sheetName)
                    .doWrite(rows);

        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new RuntimeException("导出Excel失败");
        }
    }

    /**
     * 导出实体类数据到Excel
     *
     * @param response    HttpServletResponse
     * @param fileName    文件名
     * @param sheetName   sheet名
     * @param clazz       实体类class
     * @param dataList    数据列表
     * @param <T>         实体类泛型
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName,
                                       String sheetName, Class<T> clazz, List<T> dataList) {
        try {
            // 设置响应头
            setResponseHeader(response, fileName);

            // 获取样式
            HorizontalCellStyleStrategy styleStrategy = getStyleStrategy();

            // 自定义列宽策略
            CustomWidthStyleStrategy customWidthStyleStrategy = new CustomWidthStyleStrategy();

            // 写入数据，添加自适应列宽
            EasyExcel.write(response.getOutputStream(), clazz)
                    .registerWriteHandler(styleStrategy)
                    .registerWriteHandler(customWidthStyleStrategy)
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler())
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(sheetName)
                    .doWrite(dataList);

        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new RuntimeException("导出Excel失败");
        }
    }

    /**
     * 导出多个sheet的Excel
     *
     * @param response    HttpServletResponse
     * @param fileName    文件名
     * @param sheetConfigs sheet配置列表
     */
    public static void exportMultiSheetExcel(HttpServletResponse response, String fileName,
                                             List<SheetConfig<?>> sheetConfigs) {
        try {
            // 设置响应头
            setResponseHeader(response, fileName);

            // 获取输出流
            OutputStream outputStream = response.getOutputStream();

            // 自定义列宽策略
            CustomWidthStyleStrategy customWidthStyleStrategy = new CustomWidthStyleStrategy();

            // 创建ExcelWriter
            try (ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .registerWriteHandler(customWidthStyleStrategy)
                    .build()) {
                // 获取样式
                HorizontalCellStyleStrategy styleStrategy = getStyleStrategy();

                // 循环写入每个sheet
                for (int i = 0; i < sheetConfigs.size(); i++) {
                    SheetConfig<?> config = sheetConfigs.get(i);
                    WriteSheet writeSheet = EasyExcel.writerSheet(i, config.getSheetName())
                            .head(config.getClazz())
                            .registerWriteHandler(styleStrategy)
                            .registerWriteHandler(new FreezeHeaderSheetWriteHandler())
                            .build();
                    excelWriter.write(config.getDataList(), writeSheet);
                }
            }
        } catch (Exception e) {
            log.error("导出多sheet的Excel异常", e);
            throw new RuntimeException("导出Excel失败");
        }
    }

    /**
     * 创建表格样式
     */
    public static HorizontalCellStyleStrategy getStyleStrategy() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为灰色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 字体设置
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 设置居中
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 设置响应头信息
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
    }

    /**
     * Sheet配置静态内部类
     */
    @Getter
    public static class SheetConfig<T> {
        private final String sheetName;
        private final Class<T> clazz;
        private final List<T> dataList;

        public SheetConfig(String sheetName, Class<T> clazz, List<T> dataList) {
            this.sheetName = sheetName;
            this.clazz = clazz;
            this.dataList = dataList;
        }
    }

    /**
     * 自定义列宽策略静态内部类，优化表头自适应宽度和中文字符宽度计算
     */
    public static class CustomWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {
        private final Map<Integer, Integer> columnWidthMap = new HashMap<>();
        private final Map<Integer, String> headerMap = new HashMap<>();
        private static final int MIN_WIDTH = 100;  // 最小宽度
        private static final int MAX_WIDTH = 10000; // 最大宽度，防止过宽
        private static final int DEFAULT_PADDING_UNITS = 6 * 256; // 默认填充宽度

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList,
                                      Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            int columnIndex = cell.getColumnIndex();

            String textForMeasurement = "";

            // 获取需要测量的文本
            if (Boolean.TRUE.equals(isHead)) {
                // 表头处理 - 优先从Head对象获取
                if (head != null && head.getHeadNameList() != null && !head.getHeadNameList().isEmpty()) {
                    textForMeasurement = head.getHeadNameList().get(head.getHeadNameList().size() - 1);
                } else {
                    textForMeasurement = getCellStringValue(cell);
                }

                // 计算表头宽度
                int headerWidth = MIN_WIDTH;
                if (textForMeasurement != null && !textForMeasurement.isEmpty()) {
                    headerWidth = Math.max(getStringWidth(textForMeasurement, true), MIN_WIDTH);
                    headerWidth = Math.min(headerWidth, MAX_WIDTH);
                }

                // 缓存表头文本和宽度
                headerMap.put(columnIndex, textForMeasurement);

                // 获取当前列已有的最大宽度
                Integer existingWidth = columnWidthMap.get(columnIndex);
                int finalWidth = (existingWidth == null) ? headerWidth : Math.max(existingWidth, headerWidth);

                // 更新宽度
                columnWidthMap.put(columnIndex, finalWidth);
                sheet.setColumnWidth(columnIndex, finalWidth);

            } else {
                // 内容处理
                textForMeasurement = getCellStringValue(cell);

                // 计算内容宽度
                int contentWidth = MIN_WIDTH;
                if (textForMeasurement != null && !textForMeasurement.isEmpty()) {
                    contentWidth = Math.max(getStringWidth(textForMeasurement, false), MIN_WIDTH);
                    contentWidth = Math.min(contentWidth, MAX_WIDTH);
                }

                // 获取当前列的最大宽度
                Integer currentMaxWidth = columnWidthMap.get(columnIndex);

                if (currentMaxWidth == null) {
                    // 如果还没有设置过宽度（通常不会发生，因为表头先处理）
                    columnWidthMap.put(columnIndex, contentWidth);
                    sheet.setColumnWidth(columnIndex, contentWidth);
                } else {
                    // 比较内容宽度和当前最大宽度，取较大值
                    int newWidth = Math.max(currentMaxWidth, contentWidth);

                    if (newWidth > currentMaxWidth) {
                        columnWidthMap.put(columnIndex, newWidth);
                        sheet.setColumnWidth(columnIndex, newWidth);
                    }
                }
            }
        }

        /**
         * 获取单元格字符串值
         */
        private String getCellStringValue(Cell cell) {
            if (cell == null) {
                return "";
            }

            try {
                CellType cellType = cell.getCellType();
                if (cellType == CellType.FORMULA) {
                    cellType = cell.getCachedFormulaResultType();
                }

                switch (cellType) {
                    case STRING:
                        return cell.getStringCellValue();
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            try {
                                return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
                            } catch (Exception e) {
                                return String.valueOf(cell.getNumericCellValue());
                            }
                        } else {
                            double numericValue = cell.getNumericCellValue();
                            // 判断是否为整数
                            if (numericValue == Math.floor(numericValue)) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        }
                    case BOOLEAN:
                        return String.valueOf(cell.getBooleanCellValue());
                    case BLANK:
                        return "";
                    default:
                        return "";
                }
            } catch (Exception e) {
                log.warn("获取单元格值异常", e);
                return "";
            }
        }

        /**
         * 计算字符串宽度，优化中文字符和表头字体的宽度计算
         */
        private int getStringWidth(String str, Boolean isHead) {
            if (str == null || str.isEmpty()) {
                return MIN_WIDTH;
            }

            // 基础字符单位 - 调整为更大的值
            float baseCharUnit = 100f;  // 增加基础单位
            float boldMultiplier = 1.3f;  // 粗体倍数
            float chineseMultiplier = 2.2f;  // 中文字符倍数增加
            float englishMultiplier = 1.0f;  // 英文字符倍数增加
            float numberMultiplier = 1.2f;  // 数字字符倍数增加

            float totalWidth = 0;

            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                float charWidth = baseCharUnit;

                if (isChinese(c)) {
                    // 中文字符
                    charWidth *= chineseMultiplier;
                } else if (Character.isDigit(c)) {
                    // 数字字符
                    charWidth *= numberMultiplier;
                } else {
                    // 其他字符（主要是英文）
                    charWidth *= englishMultiplier;
                }

                // 如果是表头，应用粗体倍数
                if (Boolean.TRUE.equals(isHead)) {
                    charWidth *= boldMultiplier;
                }

                totalWidth += charWidth;
            }

            // 添加更多填充宽度
            int finalWidth = (int) totalWidth + (DEFAULT_PADDING_UNITS * 2);

            // 确保在最小和最大宽度范围内，但表头优先保证足够宽度
            if (Boolean.TRUE.equals(isHead)) {
                // 表头至少保证计算出的宽度，不受MIN_WIDTH限制
                return Math.min(finalWidth, MAX_WIDTH);
            } else {
                return Math.max(MIN_WIDTH, Math.min(finalWidth, MAX_WIDTH));
            }
        }

        /**
         * 判断是否为中文字符
         */
        private boolean isChinese(char c) {
            Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
            return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                    || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                    || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                    || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                    || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                    || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                    || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
        }
    }

    /**
     * 冻结表头处理器静态内部类
     */
    public static class FreezeHeaderSheetWriteHandler implements SheetWriteHandler {

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // Sheet创建前的处理，可以在这里做一些预处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            // 冻结首行（表头行）
            sheet.createFreezePane(0, 1);

            // 可选：设置打印标题行（每页都显示表头）
            sheet.setRepeatingRows(org.apache.poi.ss.util.CellRangeAddress.valueOf("1:1"));
        }
    }
}