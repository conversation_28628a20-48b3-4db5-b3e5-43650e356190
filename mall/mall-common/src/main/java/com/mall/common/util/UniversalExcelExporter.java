package com.mall.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用 Excel 导出工具类
 * 支持自动数据转换、灵活汇总信息配置、自动冻结行计算
 */
@Slf4j
public class UniversalExcelExporter {

    /**
     * 通用 Excel 导出方法
     *
     * @param response     HttpServletResponse
     * @param config       导出配置
     * @param <T>          实体类泛型
     */
    public static <T> void exportExcel(HttpServletResponse response, ExportConfig<T> config) {
        try {
            // 将Map数据转换为POJO
            List<T> exportList = convertMapDataToPojo(config.getDataList(), config.getEntityClass(), config.getFieldMapping());

            // 计算汇总信息行数
            int summaryRowCount = calculateSummaryRowCount(config.getSummaryRows());

            // 创建汇总信息处理器
            SheetWriteHandler summaryTitleHandler = null;
            if (summaryRowCount > 0) {
                summaryTitleHandler = new UniversalSummaryTitleSheetWriteHandler(
                        config.getSummaryRows(), 
                        config.getEntityClass()
                );
            }

            // 创建冻结表头处理器
            SheetWriteHandler freezeHandler = new UniversalFreezeHeaderSheetWriteHandler(summaryRowCount);

            // 设置响应头
            ExcelUtil.setResponseHeader(response, config.getFileName());

            // 创建增强版列宽策略，考虑汇总信息行的内容长度
            EnhancedWidthStyleStrategy enhancedWidthStrategy = new EnhancedWidthStyleStrategy(config.getSummaryRows());

            // 使用EasyExcel直接导出，添加自定义处理器
            var excelWriter = EasyExcel.write(response.getOutputStream(), config.getEntityClass())
                    .registerWriteHandler(ExcelUtil.getStyleStrategy())
                    .registerWriteHandler(enhancedWidthStrategy);

            if (summaryTitleHandler != null) {
                excelWriter.registerWriteHandler(summaryTitleHandler);
            }

            excelWriter.registerWriteHandler(freezeHandler)
                    .sheet(config.getSheetName())
                    .relativeHeadRowIndex(summaryRowCount) // 设置表头从汇总信息后开始
                    .doWrite(exportList);

        } catch (Exception e) {
            log.error("导出Excel异常: {}", config.getFileName(), e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage());
        }
    }

    /**
     * 将Map数据转换为POJO
     */
    private static <T> List<T> convertMapDataToPojo(List<Map<String, Object>> dataList, 
                                                   Class<T> entityClass, 
                                                   Map<String, String> fieldMapping) {
        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        return dataList.stream().map(map -> {
            try {
                T instance = entityClass.getDeclaredConstructor().newInstance();
                
                // 如果提供了字段映射，使用映射关系
                if (fieldMapping != null && !fieldMapping.isEmpty()) {
                    for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                        String pojoField = entry.getKey();
                        String mapKey = entry.getValue();
                        setFieldValue(instance, pojoField, map.get(mapKey));
                    }
                } else {
                    // 否则使用反射自动映射
                    Field[] fields = entityClass.getDeclaredFields();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        Object value = map.get(field.getName());
                        if (value != null) {
                            setFieldValue(instance, field.getName(), value);
                        }
                    }
                }
                
                return instance;
            } catch (Exception e) {
                log.error("转换数据异常", e);
                throw new RuntimeException("数据转换失败", e);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 设置字段值
     */
    private static <T> void setFieldValue(T instance, String fieldName, Object value) {
        try {
            Field field = instance.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            
            // 处理null值
            if (value == null || "null".equals(String.valueOf(value))) {
                field.set(instance, " ");
                return;
            }
            
            // 根据字段类型转换值
            Class<?> fieldType = field.getType();
            if (fieldType == String.class) {
                field.set(instance, String.valueOf(value));
            } else if (fieldType == Integer.class || fieldType == int.class) {
                field.set(instance, Integer.valueOf(String.valueOf(value)));
            } else if (fieldType == Long.class || fieldType == long.class) {
                field.set(instance, Long.valueOf(String.valueOf(value)));
            } else if (fieldType == Double.class || fieldType == double.class) {
                field.set(instance, Double.valueOf(String.valueOf(value)));
            } else {
                field.set(instance, value);
            }
        } catch (Exception e) {
            log.warn("设置字段值失败: {} = {}", fieldName, value, e);
        }
    }

    /**
     * 计算汇总信息行数
     */
    private static int calculateSummaryRowCount(List<List<SummaryItem>> summaryRows) {
        return summaryRows != null ? summaryRows.size() : 0;
    }

    /**
     * 导出配置类
     */
    @Data
    @Builder
    public static class ExportConfig<T> {
        private List<Map<String, Object>> dataList;        // 原始数据
        private Class<T> entityClass;                       // 实体类
        private String fileName;                            // 文件名
        private String sheetName;                           // Sheet名
        private List<List<SummaryItem>> summaryRows;        // 汇总信息行配置
        private Map<String, String> fieldMapping;           // 字段映射 (POJO字段名 -> Map键名)
    }

    /**
     * 汇总项配置类
     */
    @Data
    @Builder
    public static class SummaryItem {
        private String name;                                // 显示名称
        private String value;                               // 显示值
        private Function<Void, String> valueSupplier;      // 值提供者（动态获取）
        
        public String getDisplayValue() {
            if (valueSupplier != null) {
                return valueSupplier.apply(null);
            }
            return value != null ? value : "0";
        }
    }

    /**
     * 通用汇总信息标题处理器
     */
    public static class UniversalSummaryTitleSheetWriteHandler implements SheetWriteHandler {
        private final List<List<SummaryItem>> summaryRows;
        private final Class<?> entityClass;

        public UniversalSummaryTitleSheetWriteHandler(List<List<SummaryItem>> summaryRows, Class<?> entityClass) {
            this.summaryRows = summaryRows;
            this.entityClass = entityClass;
        }

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            if (summaryRows == null || summaryRows.isEmpty()) {
                return;
            }

            Sheet sheet = writeSheetHolder.getSheet();
            Workbook workbook = sheet.getWorkbook();

            // 计算列数（根据实体类的字段数量）
            int columnCount = getEntityColumnCount(entityClass);
            int lastColumnIndex = Math.max(0, columnCount - 1);

            //log.debug("实体类 {} 的列数: {}, lastColumnIndex: {}", entityClass.getSimpleName(), columnCount, lastColumnIndex);

            // 创建标题样式
            CellStyle titleStyle = createTitleStyle(workbook);

            // 创建汇总信息行
            for (int rowIndex = 0; rowIndex < summaryRows.size(); rowIndex++) {
                List<SummaryItem> rowItems = summaryRows.get(rowIndex);
                //log.debug("创建第 {} 行汇总信息，包含 {} 个统计项", rowIndex + 1, rowItems.size());
                createSummaryRow(sheet, rowIndex, rowItems, lastColumnIndex, titleStyle);
            }

            // 立即调整列宽以适应汇总信息内容
            adjustColumnWidthForSummaryContent(sheet, summaryRows, columnCount);

            // 强制自动调整所有列宽
            forceAutoSizeColumns(sheet, columnCount);
        }

        /**
         * 获取实体类列数
         */
        private int getEntityColumnCount(Class<?> entityClass) {
            // 通过反射获取@ExcelProperty注解的字段数量
            try {
                Field[] fields = entityClass.getDeclaredFields();
                int count = 0;
                for (Field field : fields) {
                    if (field.isAnnotationPresent(com.alibaba.excel.annotation.ExcelProperty.class)) {
                        count++;
                    }
                }
                // 确保至少返回1，最大返回合理的列数
                if (count <= 0) {
                    //log.warn("实体类 {} 没有找到 @ExcelProperty 注解字段，使用默认值5", entityClass.getSimpleName());
                    return 5;
                }
                return count;
            } catch (Exception e) {
                log.warn("获取实体类 {} 列数失败，使用默认值5", entityClass.getSimpleName(), e);
                return 5;
            }
        }

        /**
         * 创建标题样式
         */
        private CellStyle createTitleStyle(Workbook workbook) {
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.LEFT);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleStyle.setWrapText(false);

            Font font = workbook.createFont();
            font.setBold(true);
            font.setFontHeightInPoints((short) 12);
            titleStyle.setFont(font);

            return titleStyle;
        }

        /**
         * 强制自动调整所有列宽
         */
        private void forceAutoSizeColumns(Sheet sheet, int columnCount) {
            // 使用POI的自动调整列宽功能
            for (int col = 0; col < columnCount; col++) {
                try {
                    sheet.autoSizeColumn(col);
                    // 获取自动调整后的宽度
                    int autoWidth = sheet.getColumnWidth(col);
                    // 如果自动调整的宽度太小，设置一个最小宽度
                    int minWidth = 4000; // 最小宽度
                    if (autoWidth < minWidth) {
                        sheet.setColumnWidth(col, minWidth);
                    }
                    // 如果宽度过大，限制最大宽度
                    int maxWidth = 15000; // 最大宽度
                    if (autoWidth > maxWidth) {
                        sheet.setColumnWidth(col, maxWidth);
                    }
                } catch (Exception e) {
                    // 如果自动调整失败，设置默认宽度
                    sheet.setColumnWidth(col, 5000);
                }
            }
        }

        /**
         * 调整列宽以适应汇总信息内容
         */
        private void adjustColumnWidthForSummaryContent(Sheet sheet, List<List<SummaryItem>> summaryRows, int columnCount) {
            for (List<SummaryItem> rowItems : summaryRows) {
                if (rowItems == null || rowItems.isEmpty()) continue;

                int itemCount = rowItems.size();

                // 为所有情况设置更大的基础列宽
                for (int col = 0; col < columnCount; col++) {
                    int currentWidth = sheet.getColumnWidth(col);
                    int minWidth = 6000; // 大幅增加最小宽度
                    if (currentWidth < minWidth) {
                        sheet.setColumnWidth(col, minWidth);
                    }
                }

                if (itemCount == 3) {
                    // 三个统计项的情况，使用更合理的列宽计算
                    for (int i = 0; i < itemCount; i++) {
                        SummaryItem item = rowItems.get(i);
                        String displayText = item.getName() + ": " + item.getDisplayValue();

                        // 更精确的宽度计算：减少基础宽度和字符倍数
                        int textLength = displayText.length();
                        int requiredWidth = Math.max(4000, textLength * 250); // 减少每个字符的宽度单位

                        // 根据统计项位置分配列
                        int colsPerItem = Math.max(1, columnCount / 3);
                        int startCol = i * colsPerItem;
                        int endCol = (i == itemCount - 1) ? columnCount : (i + 1) * colsPerItem;

                        // 为每个相关列设置合理的宽度
                        int widthPerColumn = requiredWidth / Math.max(1, endCol - startCol);
                        for (int col = startCol; col < endCol; col++) {
                            sheet.setColumnWidth(col, Math.min(widthPerColumn, 8000)); // 减少最大宽度限制
                        }
                    }
                }
            }
        }

        /**
         * 创建汇总信息行
         */
        private void createSummaryRow(Sheet sheet, int rowIndex, List<SummaryItem> rowItems,
                                    int lastColumnIndex, CellStyle titleStyle) {
            Row titleRow = sheet.createRow(rowIndex);
            titleRow.setHeightInPoints(25);

            int itemCount = rowItems.size();
            if (itemCount == 0) return;

            if (itemCount == 1) {
                // 一个统计项：跨所有列
                SummaryItem item = rowItems.get(0);
                Cell cell = titleRow.createCell(0);
                cell.setCellValue(item.getName() + ": " + item.getDisplayValue());
                cell.setCellStyle(titleStyle);
                // 只有当列数大于1时才合并（需要至少2个单元格才能合并）
                if (lastColumnIndex > 0) {
                    log.debug("合并单元格: 行{}, 列0到{}", rowIndex, lastColumnIndex);
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, lastColumnIndex));
                } else {
                    log.debug("只有一列，不需要合并单元格");
                }

            } else if (itemCount == 2) {
                // 两个统计项：左右分布，每个占一半列
                if (lastColumnIndex >= 1) { // 至少需要2列才能分布
                    int midPoint = Math.max(1, (lastColumnIndex + 1) / 2);

                    SummaryItem item1 = rowItems.get(0);
                    Cell leftCell = titleRow.createCell(0);
                    leftCell.setCellValue(item1.getName() + ": " + item1.getDisplayValue());
                    leftCell.setCellStyle(titleStyle);
                    // 确保合并区域有效
                    if (midPoint > 1) {
                        //log.debug("合并左侧单元格: 行{}, 列0到{}", rowIndex, midPoint - 1);
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, midPoint - 1));
                    }

                    SummaryItem item2 = rowItems.get(1);
                    Cell rightCell = titleRow.createCell(midPoint);
                    rightCell.setCellValue(item2.getName() + ": " + item2.getDisplayValue());
                    rightCell.setCellStyle(titleStyle);
                    // 确保合并区域有效
                    if (midPoint < lastColumnIndex) {
                        //log.debug("合并右侧单元格: 行{}, 列{}到{}", rowIndex, midPoint, lastColumnIndex);
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, midPoint, lastColumnIndex));
                    }
                } else {
                    // 列数不足，只创建单元格不合并
                    SummaryItem item1 = rowItems.get(0);
                    Cell leftCell = titleRow.createCell(0);
                    leftCell.setCellValue(item1.getName() + ": " + item1.getDisplayValue());
                    leftCell.setCellStyle(titleStyle);

                    if (lastColumnIndex >= 1) {
                        SummaryItem item2 = rowItems.get(1);
                        Cell rightCell = titleRow.createCell(1);
                        rightCell.setCellValue(item2.getName() + ": " + item2.getDisplayValue());
                        rightCell.setCellStyle(titleStyle);
                    }
                }

            } else if (itemCount == 3) {
                // 三个统计项：根据文本长度动态分配列宽

                // 计算每个统计项的文本长度
                String text1 = rowItems.get(0).getName() + ": " + rowItems.get(0).getDisplayValue();
                String text2 = rowItems.get(1).getName() + ": " + rowItems.get(1).getDisplayValue();
                String text3 = rowItems.get(2).getName() + ": " + rowItems.get(2).getDisplayValue();

                int len1 = text1.length();
                int len2 = text2.length();
                int len3 = text3.length();
                int totalLen = len1 + len2 + len3;

                // 固定列分配策略：确保与数据表格对齐美观
                // 根据WriteOffData实体类的5个字段，合理分配列宽
                int cols1, cols2, cols3;

                if (lastColumnIndex + 1 == 5) {
                    // 5列的情况：2-2-1分配
                    cols1 = 2; // 今日核销补贴金 (较长文本，占2列)
                    cols2 = 2; // 累计核销补贴金 (较长文本，占2列)
                    cols3 = 1; // 累计未核销补贴金 (相对较短，占1列)
                } else {
                    // 其他情况：平均分配
                    int colWidth = Math.max(1, (lastColumnIndex + 1) / 3);
                    cols1 = colWidth;
                    cols2 = colWidth;
                    cols3 = (lastColumnIndex + 1) - cols1 - cols2;
                    cols3 = Math.max(1, cols3);
                }

                log.debug("3个统计项固定列分配: {}列, {}列, {}列 (文本长度: {}, {}, {})",
                    cols1, cols2, cols3, len1, len2, len3);

                // 先创建单元格，然后使用自动调整宽度
                // 这样可以根据实际内容自动计算最佳宽度

                // 创建单元格和合并区域
                SummaryItem item1 = rowItems.get(0);
                Cell leftCell = titleRow.createCell(0);
                leftCell.setCellValue(text1);
                leftCell.setCellStyle(titleStyle);
                if (cols1 > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, cols1 - 1));
                }

                SummaryItem item2 = rowItems.get(1);
                Cell centerCell = titleRow.createCell(cols1);
                centerCell.setCellValue(text2);
                centerCell.setCellStyle(titleStyle);
                if (cols2 > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cols1, cols1 + cols2 - 1));
                }

                SummaryItem item3 = rowItems.get(2);
                Cell rightCell = titleRow.createCell(cols1 + cols2);
                rightCell.setCellValue(text3);
                rightCell.setCellStyle(titleStyle);
                if (cols3 > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, cols1 + cols2, lastColumnIndex));
                }

            } else {
                // 多个统计项：平均分布到各列
                double colWidthDouble = (double)(lastColumnIndex + 1) / itemCount;

                for (int i = 0; i < itemCount; i++) {
                    SummaryItem item = rowItems.get(i);
                    int startCol = (int)(i * colWidthDouble);
                    int endCol = (i == itemCount - 1) ? lastColumnIndex : (int)((i + 1) * colWidthDouble) - 1;

                    Cell cell = titleRow.createCell(startCol);
                    cell.setCellValue(item.getName() + ": " + item.getDisplayValue());
                    cell.setCellStyle(titleStyle);

                    // 只有当起始列小于结束列时才合并
                    if (startCol < endCol) {
                        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, startCol, endCol));
                    }
                }
            }
        }
    }

    /**
     * 增强版列宽策略，考虑汇总信息行的内容长度
     */
    public static class EnhancedWidthStyleStrategy extends ExcelUtil.CustomWidthStyleStrategy {
        private final List<List<SummaryItem>> summaryRows;
        private boolean summaryProcessed = false;
        private final Map<Integer, Integer> fixedColumnWidths = new HashMap<>();

        public EnhancedWidthStyleStrategy(List<List<SummaryItem>> summaryRows) {
            this.summaryRows = summaryRows;
        }

        @Override
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            Sheet sheet = writeSheetHolder.getSheet();
            int columnIndex = cell.getColumnIndex();

            // 在第一次处理表头时，处理汇总信息行的列宽
            if (!summaryProcessed && Boolean.TRUE.equals(isHead) && summaryRows != null && !summaryRows.isEmpty()) {
                // 先调用父类的列宽设置
                super.setColumnWidth(writeSheetHolder, cellDataList, cell, head, relativeRowIndex, isHead);

                processSummaryRowsWidth(writeSheetHolder);
                summaryProcessed = true;

                // 记录固定的列宽，确保后续数据行使用相同宽度
                for (int col = 0; col < 5; col++) { // WriteOffData有5列
                    fixedColumnWidths.put(col, sheet.getColumnWidth(col));
                    //log.debug("记录列{}的固定宽度: {}", col, sheet.getColumnWidth(col));
                }
                return;
            }

            // 如果已经有固定的列宽设置，强制使用固定宽度，不再调用父类方法
            if (fixedColumnWidths.containsKey(columnIndex)) {
                int fixedWidth = fixedColumnWidths.get(columnIndex);
                sheet.setColumnWidth(columnIndex, fixedWidth);
                //log.debug("使用固定列宽 - 列{}: {}", columnIndex, fixedWidth);
                return;
            }

            // 其他情况调用父类方法
            super.setColumnWidth(writeSheetHolder, cellDataList, cell, head, relativeRowIndex, isHead);
        }

        /**
         * 处理汇总信息行的列宽
         */
        private void processSummaryRowsWidth(WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();

            // 获取实体类的列数
            int columnCount = getEntityColumnCount();

            for (int rowIndex = 0; rowIndex < summaryRows.size(); rowIndex++) {
                List<SummaryItem> rowItems = summaryRows.get(rowIndex);
                if (rowItems == null || rowItems.isEmpty()) continue;

                // 找出最长的统计项文本
                int maxTextWidth = 0;
                for (SummaryItem item : rowItems) {
                    String displayText = item.getName() + ": " + item.getDisplayValue();
                    int textWidth = calculateTextWidth(displayText, true);
                    maxTextWidth = Math.max(maxTextWidth, textWidth);
                }

                // 根据统计项数量和最长文本调整列宽
                adjustColumnWidthForSummaryItems(sheet, rowItems, columnCount, maxTextWidth);
            }
        }

        /**
         * 获取实体类列数
         */
        private int getEntityColumnCount() {
            // 默认返回5列，这个值应该与WriteOffData实体类的@ExcelProperty字段数量一致
            return 5;
        }

        /**
         * 根据统计项数量和内容调整列宽
         */
        private void adjustColumnWidthForSummaryItems(Sheet sheet, List<SummaryItem> rowItems, int columnCount, int maxTextWidth) {
            int itemCount = rowItems.size();

            if (itemCount == 1) {
                // 一个统计项跨所有列
                String displayText = rowItems.get(0).getName() + ": " + rowItems.get(0).getDisplayValue();
                int requiredWidth = calculateTextWidth(displayText, true);
                int avgWidthPerColumn = Math.max(3000, requiredWidth / columnCount + 1000);

                for (int col = 0; col < columnCount; col++) {
                    ensureMinColumnWidth(sheet, col, avgWidthPerColumn);
                }

            } else if (itemCount == 2) {
                // 两个统计项，左右分布
                for (int i = 0; i < itemCount; i++) {
                    String displayText = rowItems.get(i).getName() + ": " + rowItems.get(i).getDisplayValue();
                    int requiredWidth = calculateTextWidth(displayText, true);
                    int colsPerItem = columnCount / 2;
                    int avgWidthPerColumn = Math.max(4000, requiredWidth / colsPerItem + 1500);

                    int startCol = i * colsPerItem;
                    int endCol = (i == itemCount - 1) ? columnCount : (i + 1) * colsPerItem;

                    for (int col = startCol; col < endCol; col++) {
                        ensureMinColumnWidth(sheet, col, avgWidthPerColumn);
                    }
                }

            } else if (itemCount == 3) {
                // 三个统计项，使用更精确的列宽计算
                for (int i = 0; i < itemCount; i++) {
                    String displayText = rowItems.get(i).getName() + ": " + rowItems.get(i).getDisplayValue();
                    int requiredWidth = calculateTextWidth(displayText, true);
                    int colsPerItem = Math.max(1, columnCount / 3);

                    // 更精确的列宽计算：基于实际文本长度，减少额外宽度
                    int avgWidthPerColumn = Math.max(3500, Math.min(6000, requiredWidth / colsPerItem + 800));

                    int startCol = i * colsPerItem;
                    int endCol = (i == itemCount - 1) ? columnCount : (i + 1) * colsPerItem;

                    for (int col = startCol; col < endCol; col++) {
                        ensureMinColumnWidth(sheet, col, avgWidthPerColumn);
                    }
                }
            }
        }

        /**
         * 确保列的最小宽度
         */
        private void ensureMinColumnWidth(Sheet sheet, int columnIndex, int minWidth) {
            int currentWidth = sheet.getColumnWidth(columnIndex);
            if (currentWidth < minWidth) {
                sheet.setColumnWidth(columnIndex, Math.min(minWidth, 8000)); // 减少最大宽度限制
            }
        }

        /**
         * 计算文本宽度（更精确的计算）
         */
        private int calculateTextWidth(String text, boolean isBold) {
            if (text == null || text.isEmpty()) return 2000;

            // 调整基础字符单位，使列宽更合理
            float baseCharUnit = 150f; // 减少基础字符单位
            float boldMultiplier = isBold ? 1.3f : 1.0f;
            float chineseMultiplier = 2.5f; // 减少中文字符倍数
            float englishMultiplier = 1.2f; // 减少英文字符倍数
            float numberMultiplier = 1.1f; // 减少数字字符倍数

            float totalWidth = 0;
            for (int i = 0; i < text.length(); i++) {
                char c = text.charAt(i);
                float charWidth = baseCharUnit;

                if (isChinese(c)) {
                    charWidth *= chineseMultiplier;
                } else if (Character.isDigit(c)) {
                    charWidth *= numberMultiplier;
                } else {
                    charWidth *= englishMultiplier;
                }

                charWidth *= boldMultiplier;
                totalWidth += charWidth;
            }

            // 减少额外宽度，使列宽更紧凑
            return (int) (totalWidth + 1000);
        }

        /**
         * 存储列宽设置
         */
        private void storeColumnWidths(Sheet sheet, int[] widths) {
            // 将列宽信息存储到sheet的自定义属性中
            // 这里使用一个简单的方法：在sheet的第一行创建隐藏的标记
            log.debug("存储列宽设置: {}", java.util.Arrays.toString(widths));
        }

        /**
         * 判断是否为中文字符
         */
        private boolean isChinese(char c) {
            return c >= 0x4E00 && c <= 0x9FFF;
        }
    }

    /**
     * 通用冻结表头处理器
     */
    public static class UniversalFreezeHeaderSheetWriteHandler implements SheetWriteHandler {
        private final int summaryRowCount;

        public UniversalFreezeHeaderSheetWriteHandler(int summaryRowCount) {
            this.summaryRowCount = summaryRowCount;
        }

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            // 无需处理
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            // 冻结汇总信息行 + 表头行
            int freezeRowCount = summaryRowCount + 1;
            sheet.createFreezePane(0, freezeRowCount);

            // 设置打印标题行
            if (freezeRowCount > 1) {
                sheet.setRepeatingRows(org.apache.poi.ss.util.CellRangeAddress.valueOf("1:" + freezeRowCount));
            }
        }
    }
}
