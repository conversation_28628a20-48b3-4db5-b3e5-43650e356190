package com.mall.common.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;

public class ConvertToCamelCase {
    /**
     * 将Map中的键从下划线命名转换为驼峰命名
     * 同时处理日期时间格式化
     * @param map 原始数据Map
     * @return 转换后的Map
     */
    public static Map<String, Object> convertToCamelCase(Map<String, Object> map) {
        if (map == null) {
            return new LinkedHashMap<>();
        }
        Map<String, Object> result = new LinkedHashMap<>();
        map.forEach((key, value) -> {
            // 特殊处理日期时间类型
            if (key.contains("time") && value instanceof LocalDateTime dateTime) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                value = dateTime.format(formatter);
            }

            // 判断value是否为数字类型
            try {
                if (value instanceof BigDecimal number) {
                    value = formatDecimal(number);
                } else if (value instanceof String strValue) {
                    BigDecimal number = new BigDecimal(strValue);
                    value = formatDecimal(number);
                } else if (value instanceof Float floatValue) {
                    value = formatDecimal(BigDecimal.valueOf(floatValue));
                } else if (value instanceof Double doubleValue) {
                    value = formatDecimal(BigDecimal.valueOf(doubleValue));
                } else if (value instanceof Integer intValue) {
                    value = formatDecimal(BigDecimal.valueOf(intValue));
                } else if (value instanceof Long longValue) {
                    value = formatDecimal(BigDecimal.valueOf(longValue));
                }
            } catch (Exception e) {
                // 保持原值
            }

            // 转换键名为驼峰格式
            String camelKey = underscoreToCamelCase(key);
            result.put(camelKey, value == null ? " " : value);
        });
        return result;
    }

    /**
     * 将下划线命名转换为驼峰命名
     * 例如：enterprise_id -> enterpriseId
     * @param underscore 下划线命名的字符串
     * @return 驼峰命名的字符串
     */
    public static String underscoreToCamelCase(String underscore) {
        if (underscore == null || underscore.isEmpty()) {
            return underscore;
        }

        // 如果不包含下划线，直接返回原字符串
        if (!underscore.contains("_")) {
            return underscore;
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < underscore.length(); i++) {
            char currentChar = underscore.charAt(i);

            if (currentChar == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpperCase = false;
                } else {
                    result.append(currentChar);
                }
            }
        }
        return result.toString();
    }

    // 去除末尾的0，如果小数点后全是0，则转为普通整数形式
    public static String formatDecimal(BigDecimal number) {
        if (number == null) {
            return "0";
        }
        // 去除末尾的0
        number = number.stripTrailingZeros();
        // 如果小数点后全是0，则转为普通整数形式
        return number.scale() <= 0 ? number.toPlainString() : number.toPlainString();
    }
}
