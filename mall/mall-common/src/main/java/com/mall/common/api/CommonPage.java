package com.mall.common.api;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * 通用分页数据封装
 *
 * @param <T> 分页数据项类型
 */
@Data
public class CommonPage<T> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommonPage.class);
    private static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 当前页码（从1开始计数）
     */
    private Integer pageNum;
    /**
     * 每页数据量
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 总数据条数
     */
    private Long total;
    /**
     * 当前页数据列表
     */
    private List<T> list;

    /**
     * 分页数据构造函数
     *
     * @param pageNum    当前页码
     * @param pageSize   每页数据量
     * @param totalPages 总页数
     * @param total      总数据条数
     * @param list       当前页数据列表
     */
    public CommonPage(Integer pageNum, Integer pageSize, Integer totalPages, Long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
        this.total = total;
        this.list = list;
    }

    /**
     * 将完整列表封装为单页数据（适用于不分页场景）
     *
     * @param list 完整数据集
     * @return 单页分页对象（pageNum=1，pageSize=list.size）
     */
    public static <T> CommonPage<T> restPage(List<T> list) {
        return new CommonPage<>(1, list.size(), 1, (long) list.size(), list);
    }

    /**
     * 对完整数据集进行内存分页处理
     *
     * @param fullList 完整数据集
     * @param pageNum  请求页码（从1开始）
     * @param pageSize 每页数据量
     * @return 分页结果对象（包含分页元数据和当前页数据）
     *
     * @implNote 方法特性：
     * 1. 自动处理非法参数：pageSize<1时使用默认值，pageNum<1时修正为1
     * 2. 支持空数据集处理，返回空分页对象
     * 3. 自动修正超出范围的pageNum为最大有效页码
     * 4. 返回数据子列表时使用subList避免数据拷贝
     */
    public static <T> CommonPage<T> paginate(List<T> fullList, int pageNum, int pageSize) {
        // 参数合法性校验与修正
        if (pageSize < 1) {
            LOGGER.warn("Requested pageSize {} is less than 1. Using default pageSize {}.", pageSize, DEFAULT_PAGE_SIZE);
            pageSize = DEFAULT_PAGE_SIZE;
        }

        // 页码合法性校验
        if (pageNum < 1) {
            LOGGER.warn("Requested pageNum {} is less than 1. Adjusting to 1.", pageNum);
            pageNum = 1;
        }

        // 处理空数据集场景
        if (fullList == null) {
            LOGGER.warn("Input list is null. Returning an empty page.");
            return new CommonPage<>(pageNum, pageSize, 0, 0L, Collections.emptyList());
        }

        // 计算分页元数据
        long total = fullList.size();
        if (total == 0) {
            return new CommonPage<>(pageNum, pageSize, 0, 0L, Collections.emptyList());
        }

        int totalPages = (int) Math.ceil((double) total / pageSize);

        // 修正超出最大页数的页码
        if (pageNum > totalPages) {
            LOGGER.warn("Requested pageNum {} exceeds totalPages {}. Adjusting to {}.", pageNum, totalPages, totalPages);
            pageNum = totalPages;
        }

        // 计算分页区间
        int startIndex = (pageNum - 1) * pageSize;
        if (startIndex >= total) {
            return new CommonPage<>(pageNum, pageSize, totalPages, total, Collections.emptyList());
        }

        // 获取当前页数据子集（使用subList避免数据拷贝）
        int endIndex = Math.min(startIndex + pageSize, (int) total);
        List<T> pagedList = fullList.subList(startIndex, endIndex);

        return new CommonPage<>(pageNum, pageSize, totalPages, total, pagedList);
    }
}
