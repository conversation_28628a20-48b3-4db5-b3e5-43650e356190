package com.mall.common.service;

import java.util.Map;

/**
 * 统一认证服务接口
 * 集中处理Token验证和用户信息获取
 */
public interface AuthService {
    /**
     * 验证token并获取用户信息
     * @param token JWT token
     * @return 用户信息Map，验证失败返回null
     */
    Map<String, Object> validateTokenAndGetUserInfo(String token);
    
    /**
     * 从token中获取用户名
     * @param token JWT token
     * @return 用户名，验证失败返回null
     */
    String getUsernameFromToken(String token);
    
    /**
     * 使token失效
     * @param token JWT token
     * @return 操作是否成功
     */
    boolean invalidateToken(String token);
} 