package com.mall.common.service.impl;

import com.mall.common.service.AuthService;
import com.mall.common.util.JwtTokenUtil;
import com.mall.common.util.StringUtils;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 统一认证服务实现类
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public Map<String, Object> validateTokenAndGetUserInfo(String token) {
        try {
            // 验证token并获取用户名
            String username = getUsernameFromToken(token);
            if (username == null || StringUtils.isEmpty(username)) {
                return null;
            }

            // 从Redis获取用户信息
            String userInfoKey = "user:info:" + username;
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(userInfoKey);

            // 如果Redis中没有用户信息，返回null
            if (entries == null || entries.isEmpty()) {
                return null;
            }

            // 转换为Map<String, Object>
            Map<String, Object> userInfo = new HashMap<>();
            entries.forEach((key, value) -> userInfo.put(key.toString(), value));

            return userInfo;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public String getUsernameFromToken(String token) {
        try {
            return jwtTokenUtil.getUsernameFromToken(token);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean invalidateToken(String token) {
        try {
            Claims claims = jwtTokenUtil.validateToken(token);
            // 将token加入黑名单，有效期设置为剩余时间
            long expiration = claims.getExpiration().getTime() - System.currentTimeMillis();
            if (expiration > 0) {
                redisTemplate.opsForValue().set(
                        "jwt:blacklist:" + token,
                        "invalid",
                        expiration,
                        TimeUnit.MILLISECONDS
                );
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 