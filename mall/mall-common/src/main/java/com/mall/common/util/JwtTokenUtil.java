package com.mall.common.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;

import java.util.Date;
import lombok.extern.slf4j.Slf4j;

@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "jwt")
@Data
@Slf4j
public class JwtTokenUtil implements InitializingBean {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    // 缓存密钥，确保每次使用同一个实例
    private SecretKey cachedKey;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化时预热密钥并输出一些信息
        SecretKey key = getSecretKey();
        byte[] encoded = key.getEncoded();
        log.info("JWT 密钥初始化成功, 算法: HS512, 密钥长度: {} bytes", encoded.length);
    }

    // 生成Token
    public String generateToken(String username) {
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(getSecretKey(), SignatureAlgorithm.HS512) // 使用HS512算法
                .compact();
    }

    // 验证Token
    public Claims validateToken(String token) {
        Boolean isBlacklisted = redisTemplate.hasKey("jwt:blacklist:" + token);
        if (isBlacklisted != null && isBlacklisted) {
            throw new ExpiredJwtException(null, null, "Token has been blacklisted");
        }
        return Jwts.parserBuilder()
                .setSigningKey(getSecretKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private SecretKey getSecretKey() {
        // 如果已经有缓存的密钥，直接返回
        if (cachedKey != null) {
            return cachedKey;
        }
        
        try {
            // 确保密钥足够长度以支持HS512算法 (至少64字节)
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            
            // 如果密钥太短，填充它以满足HS512要求
            if (keyBytes.length < 64) {
                byte[] paddedKey = new byte[64];
                System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
                // 用密钥的一部分填充剩余部分
                for (int i = keyBytes.length; i < 64; i++) {
                    paddedKey[i] = keyBytes[i % keyBytes.length];
                }
                keyBytes = paddedKey;
            }
            
            // 使用填充后的密钥bytes创建SecretKey
            cachedKey = Keys.hmacShaKeyFor(keyBytes);
            return cachedKey;
        } catch (Exception e) {
            // 出现异常，记录错误
            System.err.println("密钥处理异常: " + e.getMessage());
            throw e;
        }
    }
    public String getUsernameFromToken(String token) {
        Claims claims = validateToken(token);
        return claims.getSubject();
    }

}
