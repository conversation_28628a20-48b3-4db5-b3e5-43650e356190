package com.mall.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 网关启动监听器
 * 用于在网关启动完成后输出访问提示信息
 */
@Component
@Slf4j
public class GatewayStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Value("${server.port:9000}")
    private int serverPort;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("=== Mall Gateway 网关启动完成 ===");
        log.info("Java版本: {}", System.getProperty("java.version"));
        log.info("网关端口: {}", serverPort);
        log.info("服务发现: 已连接到 Nacos");
        log.info("🚀 Virtual Threads: 已启用 (Java 21特性)");
        log.info("");
        log.info("🚀 网关访问地址:");
        log.info("   健康检查: http://localhost:{}/actuator/health", serverPort);
        log.info("   服务列表: http://localhost:{}/debug/services", serverPort);
        log.info("   API 网关: http://localhost:{}/api/**", serverPort);
        log.info("");
        log.info("📋 路由规则:");
        log.info("   /api/**           -> mall-project 服务");
        log.info("   /mall-project/**  -> mall-project 服务 (兼容模式)");
        log.info("   /**               -> mall-project 服务 (默认路由)");
        log.info("");
        log.info("✅ 网关已就绪，可以正常访问！");

        // 检查关键组件是否正常
        try {
            log.info("🔍 所有组件检查完成，服务正常运行");
        } catch (Exception e) {
            log.error("❌ 启动后检查发现问题", e);
        }
    }
}
