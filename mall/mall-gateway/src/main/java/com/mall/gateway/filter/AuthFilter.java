package com.mall.gateway.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.constant.Constants;
import com.mall.common.service.AuthService;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证过滤器
 */
@Component
@Slf4j
public class AuthFilter implements GlobalFilter, Ordered {

    private final AuthService authService;

    // 白名单路径
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/user/login",
            "/user/register",
            "/debug/services",   // 添加debug接口到白名单
            "/debug/service",    // 添加debug接口到白名单
            "/captcha"           //获取验证码
    );

    private final static ObjectMapper objectMapper = new ObjectMapper();

    public AuthFilter(AuthService authService) {
        this.authService = authService;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 白名单路径直接放行
        if (isWhiteList(path)) {
            log.trace("白名单路径: {}, 直接放行", path);
            return chain.filter(exchange);     //传递给下一个过滤器处理
        }

        // 获取token
        String token = getToken(exchange);

        if (token == null || token.isEmpty()) {
            return writeJsonError(exchange, HttpStatus.FORBIDDEN, "access_denied", "未提供Token");
        }
        
        // 修改请求，确保token作为请求参数传递给下游服务
        // 创建一个包含token参数的新URI
        URI originalUri = request.getURI();
        String originalQuery = originalUri.getQuery();

        String newQuery;
        if (originalQuery == null || originalQuery.isEmpty()) {
            newQuery = "token=" + token;
        } else if (!originalQuery.contains("token=")) {
            newQuery = originalQuery + "&token=" + token;
        } else {
            // 如果已经有token参数，就不需要再添加了
            newQuery = originalQuery;
        }

        try {
            // 构建新的URI，包含token参数
            URI newUri = new URI(
                    originalUri.getScheme(),
                    originalUri.getUserInfo(),
                    originalUri.getHost(),
                    originalUri.getPort(),
                    originalUri.getPath(),
                    newQuery,
                    originalUri.getFragment()
            );

            // 使用新URI创建修改后的请求
            ServerHttpRequest modifiedRequest = request.mutate()
                    .uri(newUri)
                    .build();
                
            // 使用AuthService验证token
            String username = authService.getUsernameFromToken(token);
            if (username == null) {
                return writeJsonError(exchange, HttpStatus.FORBIDDEN, "access_denied", "无效的Token");
            }
                
            return chain.filter(exchange.mutate().request(modifiedRequest).build());
        } catch (MalformedJwtException e) {
            return writeJsonError(exchange, HttpStatus.FORBIDDEN, "access_denied", "无效Token格式");
        } catch (SignatureException e) {
            return writeJsonError(exchange, HttpStatus.FORBIDDEN, "access_denied", "Token签名验证失败");
        } catch (ExpiredJwtException e) {
            return writeJsonError(exchange, HttpStatus.FORBIDDEN, "access_denied", "Token已过期");
        } catch (Exception e) {
            return writeJsonError(exchange, HttpStatus.NOT_FOUND, "access_denied", "创建带有token参数的URI时出错");
        }
    }

    private boolean isWhiteList(String path) {
        for (String whitePath : WHITE_LIST) {
            if (path.contains(whitePath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取Token，优先级：
     * 1. 请求参数中的token
     * 2. 请求头中的Authorization
     */
    private String getToken(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();

        // 1. 从URL参数中获取
        String token = request.getQueryParams().getFirst("token");
        if (StringUtils.hasText(token)) {
            return token;
        }

        // 2. 从请求头中获取
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(Constants.TOKEN_PREFIX)) {
            token = authHeader.substring(Constants.TOKEN_PREFIX.length());
            return token;
        }
        return null;
    }

    // 通用错误响应方法
    public static Mono<Void> writeJsonError(ServerWebExchange exchange,
                                            HttpStatus status,
                                            String error,
                                            String message) {
        Map<String, Object> errorBody = new HashMap<>();
        errorBody.put("error", error);
        errorBody.put("message", message);
        errorBody.put("status", status.value());

        try {
            String jsonBody = objectMapper.writeValueAsString(errorBody);
            return writeResponse(exchange,
                    status,
                    MediaType.APPLICATION_JSON,
                    jsonBody);
        } catch (JsonProcessingException e) {
            return writeResponse(exchange,
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    MediaType.TEXT_PLAIN,
                    "Error generating error response");
        }
    }

    // 底层通用响应方法
    private static Mono<Void> writeResponse(ServerWebExchange exchange,
                                            HttpStatus status,
                                            MediaType contentType,
                                            String content) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().setContentType(contentType);

        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        return response.writeWith(Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        return 0;
    }
}