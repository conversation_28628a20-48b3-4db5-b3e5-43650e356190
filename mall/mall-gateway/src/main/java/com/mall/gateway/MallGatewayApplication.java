package com.mall.gateway;

import com.mall.common.config.CommonComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

/**
 * 网关启动程序
 */
@SpringBootApplication
@EnableDiscoveryClient
@Import({
        CommonComponentScan.class,  // 导入公共组件扫描
})
public class MallGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(MallGatewayApplication.class, args);
    }
}