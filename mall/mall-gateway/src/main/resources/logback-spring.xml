<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/base.xml" />

    <!-- 网关日志文件配置 -->
    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>mall-gateway.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>mall-gateway-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- keep 30 days' worth of history -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 开发测试环境控制台配置 - 适度的调试信息 -->
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n</pattern>
            </encoder>
            <!-- 开发测试环境：显示 INFO 及以上级别到控制台，便于问题排查 -->
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
        </appender>

        <!-- 开发测试环境根日志级别 -->
        <root level="INFO">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <!-- 产品上线环境控制台配置 - 最小化输出 -->
    <springProfile name="online">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n</pattern>
            </encoder>
            <!-- 产品上线环境：只显示 WARN 及以上级别到控制台 -->
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
        </appender>

        <!-- 产品上线环境根日志级别 -->
        <root level="WARN">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <!-- 开发测试环境应用程序日志配置 -->
    <springProfile name="dev">
        <!-- 应用程序日志 - 开发测试环境：适度输出业务日志到控制台，便于调试 -->
        <logger name="com.mall" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 网关启动信息 -->
        <logger name="com.mall.gateway.MallGatewayApplication" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 网关启动监听器日志 -->
        <logger name="com.mall.gateway.config.GatewayStartupListener" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境应用程序日志配置 -->
    <springProfile name="online">
        <!-- 应用程序日志 - 产品上线环境：业务日志只记录到文件，避免敏感信息泄露 -->
        <logger name="com.mall" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 产品上线环境：业务日志不输出到控制台 -->
        </logger>

        <!-- 网关启动信息 - 产品上线环境仍需要显示启动状态 -->
        <logger name="com.mall.gateway.MallGatewayApplication" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 网关启动监听器日志 - 产品上线环境需要显示启动完成信息 -->
        <logger name="com.mall.gateway.config.GatewayStartupListener" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>

    <!-- 开发测试环境网关核心组件日志配置 -->
    <springProfile name="dev">
        <!-- Nacos 客户端日志 -->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Gateway 日志 - 开发测试环境：网关路由信息 -->
        <logger name="org.springframework.cloud.gateway" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- 网络层日志 - 开发测试环境：记录网络信息 -->
        <logger name="reactor.netty" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- 负载均衡日志 - 开发测试环境：记录负载均衡信息 -->
        <logger name="org.springframework.cloud.loadbalancer" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境网关核心组件日志配置 -->
    <springProfile name="online">
        <!-- Nacos 客户端日志 - 只记录警告和错误 -->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Gateway 日志 - 产品上线环境：网关路由信息记录到文件 -->
        <logger name="org.springframework.cloud.gateway" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 产品上线环境：网关日志不输出到控制台 -->
        </logger>

        <!-- 网络层日志 - 产品上线环境：只记录警告和错误 -->
        <logger name="reactor.netty" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- 负载均衡日志 - 产品上线环境：只记录警告和错误 -->
        <logger name="org.springframework.cloud.loadbalancer" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 开发测试环境网关系统组件日志配置 -->
    <springProfile name="dev">
        <!-- Spring Context 相关日志 -->
        <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Gateway 路由加载日志 -->
        <logger name="org.springframework.cloud.gateway.route" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HTTP 相关日志 -->
        <logger name="org.springframework.http" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- WebFlux 相关日志 -->
        <logger name="org.springframework.web.reactive" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HTTP Server Reactive 相关日志 -->
        <logger name="org.springframework.http.server.reactive" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Client 相关日志 -->
        <logger name="org.springframework.cloud.client" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context 相关日志 -->
        <logger name="org.springframework.cloud.context" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context Scope 相关日志 -->
        <logger name="org.springframework.cloud.context.scope" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Tomcat 相关日志控制 -->
        <logger name="org.apache.catalina" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Boot Web Embedded Tomcat 相关日志控制 -->
        <logger name="org.springframework.boot.web.embedded.tomcat" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Data Repository 相关日志控制 -->
        <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Actuator 相关日志控制 -->
        <logger name="org.springframework.boot.actuate.endpoint.web" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境网关系统组件日志配置 -->
    <springProfile name="online">
        <!-- Spring Context 相关警告日志 - 只记录错误 -->
        <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Gateway 路由加载日志 - 只记录错误 -->
        <logger name="org.springframework.cloud.gateway.route" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HTTP 相关日志 - 只记录警告 -->
        <logger name="org.springframework.http" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- WebFlux 相关日志 - 只记录警告 -->
        <logger name="org.springframework.web.reactive" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HTTP Server Reactive 相关日志 - 只记录警告 -->
        <logger name="org.springframework.http.server.reactive" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Client 相关日志 -->
        <logger name="org.springframework.cloud.client" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context 相关日志 -->
        <logger name="org.springframework.cloud.context" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context Scope 相关日志 -->
        <logger name="org.springframework.cloud.context.scope" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Tomcat 相关日志控制 -->
        <logger name="org.apache.catalina" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Boot Web Embedded Tomcat 相关日志控制 -->
        <logger name="org.springframework.boot.web.embedded.tomcat" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Data Repository 相关日志控制 -->
        <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Actuator 相关日志控制 -->
        <logger name="org.springframework.boot.actuate.endpoint.web" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

</configuration>
