<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/base.xml" />

    <!-- 项目日志文件配置 -->
    <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>mall-project.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>mall-project-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- keep 30 days' worth of history -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 生产环境控制台配置 - 适度的调试信息 -->
    <springProfile name="dev">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n</pattern>
            </encoder>
            <!-- 生产环境：显示 INFO 及以上级别到控制台，便于问题排查 -->
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
        </appender>

        <!-- 生产环境根日志级别 -->
        <root level="INFO">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <!-- 产品上线环境控制台配置 - 最小化输出 -->
    <springProfile name="online">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n</pattern>
            </encoder>
            <!-- 产品上线环境：只显示 WARN 及以上级别到控制台 -->
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
        </appender>

        <!-- 产品上线环境根日志级别 -->
        <root level="WARN">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <!-- 生产环境应用程序日志配置 -->
    <springProfile name="dev">
        <!-- 应用程序日志 - 生产环境：适度输出业务日志到控制台，便于调试 -->
        <logger name="com.mall" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 应用启动信息 -->
        <logger name="com.mall.project.MallProjectApplication" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 项目启动监听器日志 -->
        <logger name="com.mall.project.config.StartupListener" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境应用程序日志配置 -->
    <springProfile name="online">
        <!-- 应用程序日志 - 产品上线环境：业务日志只记录到文件，避免敏感信息泄露 -->
        <logger name="com.mall" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 产品上线环境：业务日志不输出到控制台 -->
        </logger>

        <!-- 应用启动信息 - 产品上线环境仍需要显示启动状态 -->
        <logger name="com.mall.project.MallProjectApplication" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- 项目启动监听器日志 - 产品上线环境需要显示启动完成信息 -->
        <logger name="com.mall.project.config.StartupListener" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>
    </springProfile>

    <!-- 生产环境SQL日志配置 -->
    <springProfile name="dev">
        <!-- SQL 执行日志 - 生产环境：记录WARN级别到文件，便于性能调优 -->
        <logger name="org.springframework.jdbc.core" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 生产环境：SQL日志不输出到控制台，但记录到文件 -->
        </logger>

        <!-- JdbcTemplate 相关日志 -->
        <logger name="org.springframework.jdbc.core.JdbcTemplate" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 参数设置日志 -->
        <logger name="org.springframework.jdbc.core.StatementCreatorUtils" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 数据源相关日志 -->
        <logger name="org.springframework.jdbc.datasource" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 支持类日志 -->
        <logger name="org.springframework.jdbc.support" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境SQL日志配置 -->
    <springProfile name="online">
        <!-- SQL 执行日志 - 产品上线环境：绝对禁止SQL语句输出，只记录数据库错误 -->
        <logger name="org.springframework.jdbc.core" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 产品上线环境：SQL日志绝不输出到控制台 -->
        </logger>

        <!-- JdbcTemplate 相关日志 - 产品上线环境：只记录严重错误 -->
        <logger name="org.springframework.jdbc.core.JdbcTemplate" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 参数设置日志 - 产品上线环境：禁止参数输出（安全考虑） -->
        <logger name="org.springframework.jdbc.core.StatementCreatorUtils" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 数据源相关日志 - 产品上线环境：只记录连接错误 -->
        <logger name="org.springframework.jdbc.datasource" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- JDBC 支持类日志 - 产品上线环境：只记录错误 -->
        <logger name="org.springframework.jdbc.support" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 生产环境系统组件日志配置 -->
    <springProfile name="dev">
        <!-- Nacos 客户端日志 -->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Context 相关日志 -->
        <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Data Repository 相关日志 -->
        <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HikariCP 连接池日志 -->
        <logger name="com.zaxxer.hikari" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Tomcat 相关日志 -->
        <logger name="org.apache.catalina" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Boot Web 相关日志 -->
        <logger name="org.springframework.boot.web.embedded.tomcat" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Actuator 相关日志 -->
        <logger name="org.springframework.boot.actuate.endpoint.web" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- 定时任务相关日志 - 生产环境：显示任务执行状态 -->
        <logger name="com.mall.project.task" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <appender-ref ref="CONSOLE" />
        </logger>

        <!-- Spring 定时任务框架日志 -->
        <logger name="org.springframework.scheduling" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Client 相关日志 -->
        <logger name="org.springframework.cloud.client" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context 相关日志 -->
        <logger name="org.springframework.cloud.context" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Framework 通用日志 -->
        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

    <!-- 产品上线环境系统组件日志配置 -->
    <springProfile name="online">
        <!-- Nacos 客户端日志 - 只记录警告和错误 -->
        <logger name="com.alibaba.nacos" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Context 相关警告日志 - 只记录错误 -->
        <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="ERROR" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Data Repository 相关日志控制 -->
        <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- HikariCP 连接池日志 - 只记录警告 -->
        <logger name="com.zaxxer.hikari" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Tomcat 相关日志 - 只记录警告 -->
        <logger name="org.apache.catalina" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Boot Web 相关日志 - 只记录警告 -->
        <logger name="org.springframework.boot.web.embedded.tomcat" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Actuator 相关日志 - 只记录警告 -->
        <logger name="org.springframework.boot.actuate.endpoint.web" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- 定时任务相关日志 - 产品上线环境：任务执行状态记录到文件 -->
        <logger name="com.mall.project.task" level="INFO" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
            <!-- 产品上线环境：定时任务日志不输出到控制台 -->
        </logger>

        <!-- Spring 定时任务框架日志 - 只记录警告 -->
        <logger name="org.springframework.scheduling" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Client 相关日志 -->
        <logger name="org.springframework.cloud.client" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Cloud Context 相关日志 -->
        <logger name="org.springframework.cloud.context" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>

        <!-- Spring Framework 通用日志 - 只记录警告级别 -->
        <logger name="org.springframework" level="WARN" additivity="false">
            <appender-ref ref="DAILY_ROLLING_FILE" />
        </logger>
    </springProfile>

</configuration>