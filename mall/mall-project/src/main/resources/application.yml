server:
  port: 9001
  servlet:
    context-path: /

spring:
  application:
    name: mall-project
  main:
    banner-mode: off  # 关闭启动横幅
  # 配置环境
  profiles:
    active: dev  # 环境：dev=开发测试环境, online=产品上线环境

  # 启用Virtual Threads（Java 21特性）
  threads:
    virtual:
      enabled: true  # 启用虚拟线程

  # Web配置
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600  # 静态资源缓存时间（秒）

  # MVC配置
  mvc:
    static-path-pattern: /static/**  # 静态资源路径模式

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    username: root
    password: 123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        # 服务健康检查设置
        heart-beat-timeout: 15000
        heart-beat-interval: 5000
        # 显式启用服务注册
        register-enabled: true
        namespace: public1
        group: DEFAULT_GROUP

  # Redis配置
  data:
    redis:
      host: ************
      port: 6379
      password:
      timeout: 5000
      database: 1

# JWT配置
jwt:
  secret: "Gp21h8VE8Eql2azYuKL4vsrOzDNggxP88si8+KrJVC3bVK+qZTU96JrmyxWB6kvGhu9/4VGdmDIYUTZuKNFWUg=="
  expiration: 86400  # Token有效期(秒) 24小时
  tokenHeader: Authorization
  tokenHead: Bearer

# 定时任务配置
task:
  scheduling:
    #表达式格式:        秒 分 时 日 月 周
    daily-task-cron: "0 5 0 * * ?"  # 每天凌晨12点05分执行
    hourly-task-cron: "0 0 1 * * ?"  # 每天凌晨1点执行

# mallB系统配置
mall-b:
  api:
    base-url: http://************:8080
    username: admin
    password: vM7nGznm8co