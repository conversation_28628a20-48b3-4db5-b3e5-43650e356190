package com.mall.project.util;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Random;

public class CaptchaUtils {

    // 基础配置
    private static final int BASE_WIDTH = 140;
    private static final int WIDTH_PADDING = 10;
    private static final int HEIGHT = 40;
    private static final int FONT_SIZE = 24;
    private static final int LINE_COUNT = 15;
    private static final int TEXT_START_X = 8;
    private static final int TEXT_Y = 30;

    public static MathCaptcha generateMathCaptcha() {
        String[] expressionAndResult = generateMathExpression();
        String expression = expressionAndResult[0] + " = ?";

        int imageWidth = calculateImageWidth(expression);
        BufferedImage image = new BufferedImage(imageWidth, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, imageWidth, HEIGHT);

        drawInterferenceLines(g, imageWidth);
        drawExpression(g, expression);
        g.dispose();

        return new MathCaptcha(image, expressionAndResult[1]);
    }

    private static int calculateImageWidth(String expression) {
        BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        Graphics2D gTemp = tempImage.createGraphics();
        Font font = new Font("Arial", Font.BOLD, FONT_SIZE);
        FontMetrics metrics = gTemp.getFontMetrics(font);
        int textWidth = metrics.stringWidth(expression);
        gTemp.dispose();
        return textWidth + WIDTH_PADDING * 2;
    }

    private static void drawInterferenceLines(Graphics2D g, int imageWidth) {
        Random random = new Random();
        for (int i = 0; i < LINE_COUNT; i++) {
            g.setColor(getRandomColor(150));
            g.drawLine(random.nextInt(imageWidth), random.nextInt(HEIGHT),
                    random.nextInt(imageWidth), random.nextInt(HEIGHT));
        }
    }

    private static void drawExpression(Graphics2D g, String expression) {
        g.setFont(new Font("Arial", Font.BOLD, FONT_SIZE));
        FontMetrics metrics = g.getFontMetrics();
        int currentX = TEXT_START_X;

        for (char c : expression.toCharArray()) {
            g.setColor(getRandomColor(100));
            double theta = new Random().nextDouble() * 0.17 - 0.085;
            g.rotate(theta, currentX, TEXT_Y);
            g.drawString(String.valueOf(c), currentX, TEXT_Y);
            g.rotate(-theta, currentX, TEXT_Y);
            currentX += metrics.charWidth(c);
        }
    }

    private static String[] generateMathExpression() {
        Random random = new Random();
        String operator = getRandomOperator();
        int[] numbers = generateNumbers(operator);

        String expression = numbers[0] + " " + operator + " " + numbers[1];
        int result = operator.equals("+") ? numbers[0] + numbers[1] : numbers[0] - numbers[1];

        return new String[]{expression, String.valueOf(result)};
    }

    private static int[] generateNumbers(String operator) {
        Random random = new Random();
        if (operator.equals("+")) {
            return new int[]{
                    random.nextInt(10) + 1, // 1-10
                    random.nextInt(10) + 1  // 1-10
            };
        } else {
            // 减法运算确保第一个数 >= 第二个数
            int num2 = random.nextInt(9) + 1;  // 1-9
            int num1 = random.nextInt(10 - num2) + num2 + 1; // 生成num1 >= num2
            return new int[]{num1, num2};
        }
    }

    private static String getRandomOperator() {
        return new Random().nextBoolean() ? "+" : "-";
    }

    private static Color getRandomColor(int maxRGB) {
        Random random = new Random();
        return new Color(
                random.nextInt(maxRGB),
                random.nextInt(maxRGB),
                random.nextInt(maxRGB)
        );
    }

    public static class MathCaptcha {
        private final BufferedImage image;
        private final String result;

        public MathCaptcha(BufferedImage image, String result) {
            this.image = image;
            this.result = result;
        }

        public byte[] getImageBytes() throws IOException {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            return baos.toByteArray();
        }

        public String getResult() {
            return result;
        }
    }
}