package com.mall.project.controller.areaProportion;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.areaProportion.AreaProportion;
import com.mall.project.service.areaProportion.AreaProportionService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 区域授权比例 控制器
 */
@RestController
@RequestMapping("/api")
public class AreaProportionController {

    @Autowired
    private AreaProportionService areaProportionService;

    /**
     * 获取区域授权比例
     */
    @GetMapping("/getAreaProportion")
    public CommonResult<Map<String, Object>> getAreaProportion() {
        Map<String, Object> dataMap = areaProportionService.getAreaProportion();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到区域授权比例信息，请设置区域授权比例");
        }
    }
    /**
     * 保存或更新区域授权比例
     */
    @PutMapping("/saveOrUpdateAreaProportion")
    public CommonResult<Map<String, Object>> saveOrUpdateAreaProportion(@RequestBody @Valid AreaProportion pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = areaProportionService.saveOrUpdateAreaProportion(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }
        return CommonResult.failed("保存失败");
    }
}
