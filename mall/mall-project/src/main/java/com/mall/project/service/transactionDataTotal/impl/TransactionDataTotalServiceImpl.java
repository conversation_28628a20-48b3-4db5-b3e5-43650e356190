package com.mall.project.service.transactionDataTotal.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.transactionDataTotal.TransactionDataTotalDao;
import com.mall.project.service.transactionDataTotal.TransactionDataTotalService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TransactionDataTotalServiceImpl implements TransactionDataTotalService {

    @Autowired
    public TransactionDataTotalDao transactionDataTotalDao;

    //  查询统计累计交易数据 分页显示
    @Override
    public CommonPage<Map<String, Object>> QuerytransactionDataTotalPages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        int offset = (pageNum - 1) * pageSize;

        List<Map<String, Object>> list = transactionDataTotalDao.QuerytransactionDataTotalPages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = list.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());

        long total = transactionDataTotalDao.countTransactionDataTotal(phone, startDate, endDate);

        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        //累计交易金额
        String sumTradeAmountTotal = sumTradeAmountTotal(phone, startDate);

        CustomCommonPage<Map<String, Object>> commonPage = new CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);

        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("sumTradeAmountTotal", sumTradeAmountTotal);
        commonPage.setSummary(summary);

        return commonPage;
    }
    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }
    // 查询累计交易量
    @Override
    public String sumTradeAmountTotal(String phone,String startDate) {
        return transactionDataTotalDao.sumTradeAmountTotal(phone,startDate);
    }

    @Override
    public List<Map<String, Object>> transactionDataTotalExport(String phone, String startDate, String endDate) {
        List<Map<String, Object>> transactionDataTotals = transactionDataTotalDao.transactionDataTotalExport(phone,startDate,endDate);
        return transactionDataTotals.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
    }
}
