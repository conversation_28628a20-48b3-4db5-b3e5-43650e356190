package com.mall.project.service.operationPassword.impl;

import com.mall.project.dao.operationPassword.OperationPasswordDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.operationPassword.OperationPasswordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.mall.common.util.StringUtils.isEmpty;

/**
 * 操作密码服务实现类
 */
@Service
@Slf4j
public class OperationPasswordServiceImpl implements OperationPasswordService {


    @Autowired
    private OperationPasswordDao operationPasswordDao;


    /**
     *
     * 获取操作密码
     */
    public Map<String, Object> getOperationPassword(String phone) {
        return operationPasswordDao.getOperationPassword(phone);
    }

    /**
     * 更新或修改操作密码
     */
    public int saveOrUpdateOperationPassword(String password, String phone,Integer updatePerson) {
        //操作密码不能为空，且至少为6个字符
        if(isEmpty(password) || password.length() < 6) {
            throw new BusinessException("操作密码不能为空，且至少为6个字符");
        }
        return operationPasswordDao.saveOrUpdateOperationPassword(password,phone, updatePerson);
    }

    /**
     * 验证操作密码
     */
    @Override
    public boolean verifyOperationPassword(String password, String phone) {
        return operationPasswordDao.verifyOperationPassword(password, phone);
    }
}
