package com.mall.project.service.partnerEnterpriseAdminData;

import com.mall.project.dto.partnerEnterpriseAdminData.PartnerEnterpriseAdminData;

import java.util.Map;

/**
 * 所有合作企业Admain的各ID每日每笔数据量化数比接口
 */
public interface PartnerEnterpriseAdminDataService {

    /**
     * 获取所有合作企业Admain的各ID每日每笔数据量化数比
     */
    public Map<String, Object> getPartnerEnterpriseAdminData();

    /**
     * 添加所有合作企业Admain的各ID每日每笔数据量化数比
     */
    public Map<String, Object> saveOrUpdatePartnerEnterpriseAdminData(PartnerEnterpriseAdminData pojo, Integer updatePerson);
}
