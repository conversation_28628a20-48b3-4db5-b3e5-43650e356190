package com.mall.project.service.cQuantifyEvolve.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.cQuantifyEvolve.CQuantifyEvolveDao;
import com.mall.project.service.cQuantifyEvolve.CQuantifyEvolveService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class CQuantifyEvolveServiceImpl implements CQuantifyEvolveService {

    @Autowired
    private CQuantifyEvolveDao cQuantifyEvolveDao;

    /**
     * 计算用户类型为C的量化值进化量
     */
    @Override
    public void updateCQuantifyEvolve() {
        cQuantifyEvolveDao.updateCQuantifyEvolve();
    }
    /**
     * 查询C用户量化值进化量, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryCQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = cQuantifyEvolveDao.queryCQuantifyEvolvePages(phone, startDate, endDate, pageSize, offset, isGreaterThanZero);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = cQuantifyEvolveDao.totalCQuantifyEvolve(phone, startDate, endDate, isGreaterThanZero);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        CQuantifyEvolveServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new CQuantifyEvolveServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayCQuantifyEvolve", todayCQuantifyEvolve(phone, startDate));   //今日C量化值进化量
        summary.put("totalCQuantifyEvolve", totalCQuantifyEvolve(phone, startDate));   //累计C量化值进化量
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }


    /**
     * C用户量化值进化量, 导出 Excel
     */
    @Override
    public List<Map<String, Object>> exportCQuantifyEvolveExcel(String phone, String startDate, String endDate) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return cQuantifyEvolveDao.exportCQuantifyEvolveExcel(phone,startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }
    /**
     * 今日C量化值进化量
     */
    @Override
    public String todayCQuantifyEvolve(String phone, String startTime) {
        return cQuantifyEvolveDao.todayTotalQuantifyEvolve(phone,startTime) == null ? "0" : cQuantifyEvolveDao.todayTotalQuantifyEvolve(phone, startTime);
    }
    /**
     * 累计C量化值进化量
     */
    @Override
    public String totalCQuantifyEvolve(String phone, String startTime) {
        return cQuantifyEvolveDao.totalQuantifyEvolve(phone,startTime) == null ? "0" : cQuantifyEvolveDao.totalQuantifyEvolve(phone, startTime);
    }
}
