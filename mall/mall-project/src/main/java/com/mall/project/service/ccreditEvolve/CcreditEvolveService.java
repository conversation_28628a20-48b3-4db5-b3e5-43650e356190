package com.mall.project.service.ccreditEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * C量化进化量服务接口
 */
public interface CcreditEvolveService {

    /**
     * 计算C量化进化量
     */
    public void updateCreditEvolve();

    /**
     * 查询C量化进化量,分页显示
     */
    public CommonPage<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero);

    /**
     * C量化进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone, String startDate, String endDate);

    /**
     * 今日C总量化进化量
     */
    public String todayTotalCreditEvolve(String phone, String startDate);

    /**
     * 累计C量化进化量
     */
    public String totalCreditEvolve(String phone, String startDate);
}
