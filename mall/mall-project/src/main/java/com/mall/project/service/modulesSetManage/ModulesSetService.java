package com.mall.project.service.modulesSetManage;


import com.mall.project.dto.modulesSetManage.ModulesParam;

import java.util.List;

/**
 * 功能模块服务接口
 */
public interface ModulesSetService {
    /**
     * 获取所有模块列表
     */
    List<ModulesParam> listAll();
    
    /**
     * 根据ID获取模块
     */
    ModulesParam getById(Integer id);
    
    /**
     * 创建模块
     */
    boolean create(ModulesParam modules);
    
    /**
     * 更新模块
     */
    boolean update(ModulesParam modules);
    
    /**
     * 删除模块
     */
    boolean delete(Integer id);
    
    /**
     * 根据父ID获取子模块列表
     */
    List<ModulesParam> listByParentId(Integer parentId);
    
    /**
     * 获取模块路径
     */
    String getModulePath(String moduleCode);
    
    /**
     * 生成模块代码
     * @param level 模块级别
     * @param parentId 父模块ID
     * @param moduleId 当前模块ID（编辑模式时使用，新增时为null）
     * @return 生成的模块代码
     */
    String generateModuleCode(int level, Integer parentId, Integer moduleId);
    
    /**
     * 检查模块代码是否可用
     * @param moduleCode 模块代码
     * @param excludeId 需要排除的ID（更新时排除自身）
     * @return 是否可用
     */
    boolean isModuleCodeAvailable(String moduleCode, Integer excludeId);
    
    /**
     * 生成下一个可用的排序值
     * @param level 模块级别
     * @param parentId 父模块ID
     * @return 下一个排序值
     */
    int generateNextSortOrder(int level, Integer parentId);
} 