package com.mall.project.service.quantifyEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 量化值进化量服务接口
 */
public interface QuantifyEvolveService {

    /**
     * 计算用户类型为C的量化值进化量
     */
    public void updateQuantifyEvolve();

    /**
     * 查询量化值进化量, 分页显示
     */
    public CommonPage<Map<String, Object>> queryQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero);

    /**
     * 量化值进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportQuantifyEvolveExcel(String phone, String startDate, String endDate);

    /**
     * 今日总量化值进化量
     */
    public String todayTotalQuantifyEvolve(String phone, String startTime);

    /**
     * 累计量化值进化量
     */
    public String totalQuantifyEvolve(String phone, String startTime);


    /**
     * 今日Admin量化值进化量
     */
    public String todayAdminQuantifyEvolve(String startTime);

    /**
     * 累计Admin量化值进化量
     */
    public String totalAdminQuantifyEvolve(String startTime);

    /**
     * 去mallB系统读取用户类型为B的量化值进化量
     */
    public void getQuantifyEvolveFromMallB();

    /**
     * 去mallB系统读取 Admin量化值进化量
     */
    public void getAdminQuantifyEvolveFromMallB();
}
