package com.mall.project.controller.operationPassword;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.operationPassword.OperationPassword;
import com.mall.project.service.operationPassword.OperationPasswordService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 操作密码控制器
 */
@RestController
@RequestMapping("/api")
public class OperationPasswordController {

    @Autowired
    private OperationPasswordService operationPasswordService;

    /**
     * 查看当前用户有没有设置操作密码
     */
    @GetMapping("/getOperationPasswordStatus")
    public CommonResult<Map<String, Object>> getOperationPasswordStatus(@CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = operationPasswordService.getOperationPassword(userInfo.get("phone").toString());
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到操作密码信息，请设置操作密码");
        }
    }

    /**
     * 更新或修改操作密码
     */
    @PutMapping("/saveOrUpdateOperationPassword")
    public CommonResult<String> saveOrUpdateOperationPassword(@RequestBody @Valid OperationPassword pojo, @CurrentUser Map<String, Object> userInfo) {
        int result = operationPasswordService.saveOrUpdateOperationPassword(pojo.getPassword(),userInfo.get("phone").toString(),Integer.parseInt(userInfo.get("id").toString()));
        if (result > 0) {
            return CommonResult.success("保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }

    /**
     * 验证操作密码
     */
    @PostMapping("/verifyOperationPassword")
    public CommonResult<String> verifyOperationPassword(@RequestBody @Valid OperationPassword pojo, @CurrentUser Map<String, Object> userInfo) {
        boolean result = operationPasswordService.verifyOperationPassword(pojo.getPassword(),userInfo.get("phone").toString());
        if (result) {
            return CommonResult.success("验证成功");
        }else{
            return CommonResult.failed("验证失败");
        }
    }
}
