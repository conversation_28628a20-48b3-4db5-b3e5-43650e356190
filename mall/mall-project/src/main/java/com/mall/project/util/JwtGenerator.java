package com.mall.project.util;

import com.mall.common.util.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * JWT生成器
 */
@Component
public class JwtGenerator {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 生成JWT token
     * @param username 用户名
     * @return JWT token
     */
    public String generateToken(String username) {
        return jwtTokenUtil.generateToken(username);
    }
}