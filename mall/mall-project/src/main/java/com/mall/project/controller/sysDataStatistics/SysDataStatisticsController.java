package com.mall.project.controller.sysDataStatistics;

import com.mall.common.api.CommonResult;
import com.mall.project.dto.sysDataStatistics.SysDataStatistics;
import com.mall.project.service.sysDataStatistics.SysDataStatisticsService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 系统数据统计控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class SysDataStatisticsController {

    @Autowired
    private SysDataStatisticsService sysDataStatisticsService;

    /**
     * 系统数据统计
     */
    @PostMapping("/statisticsData")
    public CommonResult<Map<String, Object>> statisticsData(@RequestBody @Valid SysDataStatistics pojo) {
        Map<String, Object> result = sysDataStatisticsService.statisticsData(pojo.getOperate(), pojo.getType(), pojo.getDataTypeName());
        if (result != null) {
            return CommonResult.success(result);
        } else {
            return CommonResult.failed("统计失败");
        }
    }
}
