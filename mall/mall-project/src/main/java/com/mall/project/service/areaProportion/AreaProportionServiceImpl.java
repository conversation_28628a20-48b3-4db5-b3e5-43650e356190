package com.mall.project.service.areaProportion;

import com.mall.project.dao.areaProportion.AreaProportionDao;
import com.mall.project.dto.areaProportion.AreaProportion;
import com.mall.project.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.mall.common.util.StringUtils.isEmpty;
import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 区域授权比例服务实现类
 */
@Service
@Slf4j
public class AreaProportionServiceImpl implements AreaProportionService {

    @Autowired
    private AreaProportionDao areaProportionDao;

    /**
     * 获取区域授权比例
     */
    @Override
    public Map<String, Object> getAreaProportion() {
        return areaProportionDao.getAreaProportion();
    }
    /**
     * 保存或更新区域授权比例
     */
    @Override
    public Map<String, Object> saveOrUpdateAreaProportion(AreaProportion pojo, Integer updatePerson) {
        if(isNotEmpty(pojo.getLvel1Proportion()) && !pojo.getLvel1Proportion().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("省授权比例只能为正整数或小数，且最多保留4位小数");
        }
        if(isNotEmpty(pojo.getLvel2Proportion()) && !pojo.getLvel2Proportion().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("市授权比例只能为正整数或小数，且最多保留4位小数");
        }
        if(isNotEmpty(pojo.getLvel3Proportion()) && !pojo.getLvel3Proportion().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("县、区授权比例只能为正整数或小数，且最多保留4位小数");
        }
        if(isNotEmpty(pojo.getLvel4Proportion()) && !pojo.getLvel4Proportion().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("镇、街授权比例只能为正整数或小数，且最多保留4位小数");
        }
        if(isEmpty(pojo.getOnOff()) || !pojo.getOnOff().matches("^[01]$")){
            throw new BusinessException("区域授权比例开关不能为空，且只能为0或1");
        }
        int result = areaProportionDao.saveOrUpdateAreaProportion(pojo, updatePerson);
        if(result > 0){
            return getAreaProportion();
        }else{
            return null;
        }
    }
}
