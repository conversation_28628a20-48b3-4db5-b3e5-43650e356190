package com.mall.project.service.user.impl;

import com.mall.common.service.AuthService;
import com.mall.common.util.StringUtils;
import com.mall.project.dao.user.UserDao;
import com.mall.project.dto.user.UserRegisterParam;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.captcha.RedisCaptchaService;
import com.mall.project.service.user.UserService;
import com.mall.project.util.JwtGenerator;
import com.mall.project.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.NetworkInterface;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private JwtGenerator jwtGenerator;

    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private AuthService authService;

    @Value("${jwt.expiration}")
    private Long expiration;

    @Autowired
    private MD5Util md5Util;

    @Autowired
    private RedisCaptchaService redisCaptchaService;

    @Override
    @Transactional
    public int register(UserRegisterParam userRegisterParam) {
        String phone = userRegisterParam.getPhone();
        // 验证手机号格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确");
        }
        if (phone.length() != 11) {
            throw new BusinessException("手机号长度必须为11位");
        }
        
        if(userDao.checkMobileExists(userRegisterParam.getPhone())){
            return -1;
        }

        boolean checkEmployeeNoExists = false;
        Long employeeNo = null;
        do{
            employeeNo = (long) (Math.random() * 900000 + 100000);
            checkEmployeeNoExists = userDao.checkEmployeeNoExists(employeeNo);
        }while(checkEmployeeNoExists);

        /*try {
            // 生成登录二维码内容
            String qrCodeContent = "用户: " + "admin" + ", Token: " + "123456";
            // 使用QRCodeUtil生成二维码
            String fileName = new QRCodeUtil().generateQRCode(qrCodeContent, 300, 300);
            userRegisterParam.setQrCode(fileName);
        } catch (Exception e) {
            // 保存图片失败不影响登录流程，只记录错误
            System.out.println("保存二维码图片失败: {}"+ e.getMessage());
        }*/
        // 密码加密
        userRegisterParam.setPassword(md5Util.encryptMd5(userRegisterParam.getPhone(), userRegisterParam.getPassword()));
        // 注册用户
        return userDao.register(
                userRegisterParam.getEmployeeName(),
                userRegisterParam.getPhone(),
                employeeNo,
                userRegisterParam.getPassword(),
                userRegisterParam.getPositionId(),
                userRegisterParam.getModulesIds()
        );
    }

    /**
     *   更新用户信息
     */
    @Override
    public int updateEmployeeInfo(Long employeeId, String employeeName, String phone, String password, Long positionId, String[] modulesIds) {
        // 参数验证
        if (employeeId == null || employeeId <= 0){
            throw new BusinessException("职员ID不能为空");
        }
        if (!employeeName.matches("^[a-zA-Z0-9\\u4e00-\\u9fa5]+$")) {
            throw new BusinessException("职员名称不能包含特殊字符");
        }
        if (password != null && !password.trim().isEmpty()) {
            // 密码加密
            password = md5Util.encryptMd5(phone, password);
            return userDao.updateEmployeeInfo(
                    employeeId,
                    employeeName,
                    phone,
                    password,
                    positionId,
                    modulesIds
            );
        }else{
            return userDao.updateEmployeeInfo(
                    employeeId,
                    employeeName,
                    phone,
                    "",
                    positionId,
                    modulesIds
            );
        }
    }

    /**
     *   删除用户信息
     */
    @Override
    public int deleteEmployeeInfo(Long employeeId) {
        return userDao.deleteEmployeeInfo(employeeId);
    }

    // 按职员名称或手机号搜索用户信息
    @Override
    public List<Map<String, Object>> searchEmployeeInfo(String employeeName, String phone) {
        List<Map<String, Object>> userData = userDao.searchEmployeeInfo(employeeName, phone);
        if (userData == null || userData.isEmpty()) {
            return null;
        }
        userData.forEach(data -> {
            // 处理 datetime 字段
            Object dateTimeObj = data.get("create_time"); // 假设字段名为 create_time
            if (dateTimeObj instanceof LocalDateTime dateTime) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formattedDateTime = dateTime.format(formatter);
                data.put("create_time", formattedDateTime);
            }
        });
        return userData;
    }



    public Map<String, Object> login(String username, String password, String uuid, String captcha) {
        Map<String, Object> user = new HashMap<>();
        boolean isValid = redisCaptchaService.validateCaptcha(uuid, captcha);
        if (!isValid) {   //  验证码验证失败
            user.put("result","0");
            return user;
        }

        // 先检查该用户是否已有活跃token（强制单设备登录场景）
        String oldToken = redisTemplate.opsForValue().get("login:token:" + username);
        if (oldToken != null) {
            authService.invalidateToken(oldToken); // 使旧token失效
        }
        

        // 验证用户
        user = userDao.login(username, password);
        if (user == null) {
            user = new HashMap<>();
            user.put("result","1");
            user.put("msg", "用户名或密码不正确");
            return user;
        }
        // 检查用户状态 是否被禁用
        if (user.get("login_limit") != null && user.get("login_limit").toString().equals("true")) {
            user.put("result","3");
            user.put("msg", "该用户已被禁用");
            return user;
        }
        // 处理 datetime 字段
        Object dateTimeObj = user.get("create_time"); // 假设字段名为 create_time
        if (dateTimeObj instanceof LocalDateTime dateTime) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = dateTime.format(formatter);
            user.put("create_time", formattedDateTime);
        }
        String token = jwtGenerator.generateToken(username);
        user.put("token", token);

        // 存储到 Redis，键格式示例：login:token:username
        String redisKey = "login:token:" + username;
        redisTemplate.opsForValue().set(
                redisKey,
                token,
                expiration, // 使用与 JWT 一致的过期时间
                TimeUnit.SECONDS
        );

        // 存储用户信息到 Redis（Hash 结构）
        String userInfoKey = "user:info:" + username;

        // 转换 user 中的键值对为字符串类型
        Map<String, String> stringifiedUser = new HashMap<>();
        user.forEach((key, value) -> stringifiedUser.put(key, value != null ? value.toString() : null));
        redisTemplate.opsForHash().putAll(userInfoKey, stringifiedUser); // 将 Map 存入 Hash
        redisTemplate.expire(userInfoKey, expiration, TimeUnit.SECONDS); // 设置相同过期时间
        // 更新用户登录设备Mac 地址
        try {
            String macAddress = getMacAddress();
            userDao.updateLoginDevice(Long.parseLong(user.get("id").toString()), macAddress);
        } catch (Exception e) {
            user.put("result","2");
            user.put("msg", "更新登录设备Mac 地址失败");
            return user;
        }
        user.put("result","200");
        return user;
    }

    @Override
    public boolean logout(String token) {
        try {
            String username = authService.getUsernameFromToken(token);
            if (StringUtils.isEmpty(username)) {
                return false;
            }
            // 1. 删除登录token
            redisTemplate.delete("login:token:" + username);

            // 2. 删除用户信息缓存
            redisTemplate.delete("user:info:" + username);

            // 3. 将token加入黑名单
            return authService.invalidateToken(token);
        } catch (Exception e) {
            log.error("注销失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserALLUpLevel(Long userId) {
        List<Map<String, Object>> userChain = userDao.getUserALLUpLevel(userId);
        Map<String, Object> result = new HashMap<>();
        if (userChain != null && !userChain.isEmpty()) {
            result.put("relationshipChain", userChain);
            result.put("count", userChain.size());
            return result;
        }
        return null;
    }
    //  更新登录限制,限制登录开关
    @Override
    public int updateLoginLimit(Long employeeId, int loginLimit) {
        if (loginLimit != 0 && loginLimit != 1) {
            throw new BusinessException("登录限制只能为0或1");
        }
        return userDao.updateLoginLimit(employeeId, loginLimit);
    }

    @Override
    public List<Map<String, Object>> getEmployeesForExport() {
        return userDao.getAllEmployeesForExport();
    }

    // 根据员工ID获取员工信息: 名字,手机号,工号,岗位ID,创建时间,限制登入
    @Override
    public Map<String, Object> getEmployeeById(Long employeeId) {
        if (employeeId == null) {
            throw new BusinessException("员工编号不能为空");
        }
        // 转换 create_time 为字符串
        Map<String, Object> user = userDao.getEmployeeById(employeeId);
        if (user == null) {
            throw new BusinessException("员工编号不存在");
        }
        // 处理 datetime 字段
        Object dateTimeObj = user.get("create_time"); // 假设字段名为 create_time
        if (dateTimeObj instanceof LocalDateTime dateTime) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = dateTime.format(formatter);
            user.put("create_time", formattedDateTime);
        }
        return user;
    }

    public String getMacAddress() throws Exception {
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface network = networkInterfaces.nextElement();
            byte[] mac = network.getHardwareAddress();
            if (mac != null) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
                }
                return sb.toString();
            }
        }
        return null;
    }
}
