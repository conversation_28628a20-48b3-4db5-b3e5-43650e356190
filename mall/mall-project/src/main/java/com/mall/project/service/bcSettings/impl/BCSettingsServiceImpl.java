package com.mall.project.service.bcSettings.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.bcSettings.BCSettingsDao;
import com.mall.project.dto.bcSettings.BCSettings;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.bcSettings.BCSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 描述 : 合作企业各ID B/C设置 服务实现类
 */
@Service
@Slf4j
public class BCSettingsServiceImpl implements BCSettingsService {

    @Autowired
    private BCSettingsDao bcSettingsDao;


    // 查询B设置信息
    @Override
    public Map<String, Object> getBSettings() {
        Map<String, Object> bSettings = bcSettingsDao.getBSettings();
        if(bSettings == null || bSettings.isEmpty()){
            return null;
        }
        // 转换下划线格式为驼峰格式
        bSettings = ConvertToCamelCase.convertToCamelCase(bSettings);
        return bSettings;
    }
    // 保存或更新B设置信息
    @Override
    public Map<String, Object> saveOrUpdateBSettings(BCSettings pojo, Integer updatePerson) {
        if(pojo.getIsEnabled() == null || pojo.getIsEnabled().isEmpty() || !pojo.getIsEnabled().matches("^[01]$")) {
            throw new BusinessException("合作企业各IDB设置开、关不能为空，且只能为0或1");
        }
        if(pojo.getQuantifyToCredit() != null && pojo.getQuantifyToCredit().isEmpty() || !pojo.getQuantifyToCredit().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("合作企业各IDB设置量化值进化信用值只能为正整数或小数，且最多保留4位小数");
        }
        if(pojo.getCreditToCoupon() != null && pojo.getCreditToCoupon().isEmpty() || !pojo.getCreditToCoupon().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("合作企业各IDB设置信用值进化平台促销券只能为正整数或小数，且最多保留4位小数");
        }
        int  result = bcSettingsDao.saveOrUpdateBSettings(pojo, updatePerson);
        if(result > 0){
            return getBSettings();
        }else{
            return null;
        }
    }
    // 查询C设置信息
    @Override
    public Map<String, Object> getCSettings() {
        Map<String, Object> cSettings = bcSettingsDao.getCSettings();
        if(cSettings == null || cSettings.isEmpty()){
            return null;
        }
        // 转换下划线格式为驼峰格式
        cSettings = ConvertToCamelCase.convertToCamelCase(cSettings);
        return cSettings;
    }
    // 保存或更新C设置信息
    @Override
    public Map<String, Object> saveOrUpdateCSettings(BCSettings pojo, Integer updatePerson) {
        if(pojo.getIsEnabled() == null || pojo.getIsEnabled().isEmpty() || !pojo.getIsEnabled().matches("^[01]$")) {
            throw new BusinessException("合作企业各IDC设置开、关不能为空，且只能为0或1");
        }
        if(pojo.getQuantifyToCredit() != null && pojo.getQuantifyToCredit().isEmpty() || !pojo.getQuantifyToCredit().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("合作企业各IDC设置C每ID每日自动量化值进化信用值只能为正整数或小数，且最多保留4位小数");
        }
        if(pojo.getCreditToCoupon() != null && pojo.getCreditToCoupon().isEmpty() || !pojo.getCreditToCoupon().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("合作企业各IDC设置C每ID每日自动信用值进化平台兑换金只能为正整数或小数，且最多保留4位小数");
        }
        int  result = bcSettingsDao.saveOrUpdateCSettings(pojo, updatePerson);
        if(result > 0){
            return getCSettings();
        }else{
            return null;
        }
    }
}
