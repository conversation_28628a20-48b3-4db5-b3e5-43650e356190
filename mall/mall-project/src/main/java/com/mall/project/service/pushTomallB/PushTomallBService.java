package com.mall.project.service.pushTomallB;

public interface PushTomallBService {

    /**
     * 把量化率推送给mallB系统
     */
    public void pushQuantizationRate();

    /**
     * 推送B/C授权到mallB系统
     */
    public void pushAreaAuthorize();

    /**
     * 查询B,CB,admin的每日量化值,并推送到mallB系统
     */
    public void pushQuantizationValue();

    /**
     * 推送平台补贴金到mallB系统
     */
    public void pushPlatformGold();

    /**
     * 推送每日Admin量化值到mallB系统
     */
    public void pushAdminDailyQuantifyValue();
}
