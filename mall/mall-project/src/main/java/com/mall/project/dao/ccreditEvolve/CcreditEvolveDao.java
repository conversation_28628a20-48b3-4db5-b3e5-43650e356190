package com.mall.project.dao.ccreditEvolve;

import com.mall.project.service.bcSettings.BCSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class CcreditEvolveDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BCSettingsService bcSettingsService;

    /**
     * 量化进化量进化
     */
    public void updateCreditEvolve() {
        // 只对用户类型为C的进行量化值进化的计算, 因为B用户量化进化量要从mallB系统读取  CreditEvolveServiceImpl.getCreditEvolveFromMallB()
        String sql = "SELECT v.phone,v.credit_Value FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone and u.user_type = 'C'\n" +
                "AND DATE(v.update_date) = CURDATE() ";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
        //查询C设置
        Map<String, Object> cSettings = bcSettingsService.getCSettings();
        // 合作企业各IDC设置 的开关
        String isEnabled = cSettings.get("isEnabled").toString();
        // C每ID每日自动信用值进化平台补贴金%
        String creditToCoupon = cSettings.get("creditToCoupon").toString();
        if (isEnabled.equals("0")) {
            for (Map<String, Object> map : list) {
                String phone = (String) map.get("phone");
                //今日信用值
                BigDecimal creditValue = new BigDecimal(map.get("credit_Value").toString());
                //平台补贴金 = 今日信用值 * C每ID每日自动信用值进化平台补贴金%
                BigDecimal platformGold = creditValue.multiply(new BigDecimal(creditToCoupon)).setScale(2, RoundingMode.DOWN).divide(new BigDecimal(100));
                //今日量化值进化 = 今日信用值 - 平台补贴金
                BigDecimal creditEvolve = creditValue.subtract(platformGold).setScale(2, RoundingMode.DOWN);
                //使用 SELECT EXISTS 判断ccredit_evolve 表量化值进化量表中是否已经存在 CURDATE() - INTERVAL 1 DAY 的数据 如果存在则更新,不存在则插入
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM ccredit_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
                    String insertSql = "INSERT INTO ccredit_evolve(phone,credit_evolve,credit_evolve_total,update_date)VALUES(?,?,?,CURDATE() )";
                    jdbcTemplate.update(insertSql, phone, creditEvolve, creditEvolve.add(new BigDecimal(sumCreditEvolve(phone))).setScale(2, RoundingMode.DOWN));
                }else{
                    String updateSql = "UPDATE ccredit_evolve SET credit_evolve = ?,credit_evolve_total = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
                    jdbcTemplate.update(updateSql, creditEvolve, creditEvolve.add(new BigDecimal(sumCreditEvolve(phone))).setScale(2, RoundingMode.DOWN), phone);
                }
            }
        }
    }
    /**
     * 统计 ccredit_evolve_total ,phone 的和
     */
    public String sumCreditEvolve(String phone) {
        try{
            String sql = "SELECT credit_evolve_total FROM ccredit_evolve c WHERE c.phone = ? AND c.update_date = (select max(update_date) from ccredit_evolve r where c.phone =r.phone and r.update_date < CURDATE())";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }
    /**
     * 查询量化进化量,分页显示
     */
    public List<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int limit, int offset, Integer isGreaterThanZero) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.credit_evolve,e.credit_evolve_total FROM ccredit_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone where 1 = 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() ";
        }
        // 1.今日量化进化量  2.累计量化进化量
        if (isGreaterThanZero != null && isGreaterThanZero == 1) {
            sql += " AND e.credit_evolve > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
            sql += " AND e.credit_evolve_total > 0";
        }
        sql += " ORDER BY e.update_date DESC,e.id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 量化进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.credit_evolve,e.credit_evolve_total FROM ccredit_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone where 1 = 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() ";
        }
        sql += " ORDER BY e.update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 量化进化量总条数
     */
    public int totalCreditEvolve(String phone, String startDate, String endDate, Integer isGreaterThanZero) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM ccredit_evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        // 1.今日量化进化量  2.累计量化进化量
        if (isGreaterThanZero != null && isGreaterThanZero == 1) {
            sql += " AND credit_evolve > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
            sql += " AND credit_evolve_total > 0";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 今日总量化进化量
     */
    public String todayTotalCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        try{
            String sql = "SELECT sum(credit_evolve) as today_total_credit_evolve FROM ccredit_evolve WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND update_date = ?";
                params.add(startTime);
            }else{
                sql += " AND update_date = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        }catch (Exception e){
            return "0";
        }
    }

    /**
     * 累计量化进化量
     */
    public String totalCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        try{
            String sql = "SELECT SUM(credit_evolve_total) AS total_credit_evolve\n" +
                    "FROM (\n" +
                    "    SELECT \n" +
                    "        credit_evolve_total,\n" +
                    "        ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY phone \n" +
                    "            ORDER BY update_date DESC, id DESC\n" +
                    "        ) AS rn\n" +
                    "    FROM ccredit_evolve WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND update_date = ?";
                params.add(startTime);
            }else{
                sql += " AND update_date = CURDATE() - INTERVAL 1 DAY";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        }catch (Exception e) {
            return "0";
        }
    }
}
