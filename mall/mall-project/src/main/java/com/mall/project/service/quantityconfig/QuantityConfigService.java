package com.mall.project.service.quantityconfig;

import com.mall.project.dto.quantityconfig.QuantityConfigParam;

/**
 * 分量配置服务接口
 * <p>
 * 定义了分量配置相关的业务逻辑操作。
 * </p>
 */
public interface QuantityConfigService {

    /**
     * 获取当前的分量配置。
     * <p>
     * 系统中应仅有一条分量配置记录。此方法用于获取该配置。
     * </p>
     * @return 当前的 QuantityConfig 对象；如果配置尚未设置，可能返回 null。
     */
    QuantityConfigParam getQuantityConfig();

    /**
     * 保存或更新分量配置。
     * <p>
     * 如果系统中已存在分量配置，则更新该配置。
     * 如果尚不存在，则新增一条配置记录。
     * 操作成功后，返回包含最新数据的 QuantityConfig 对象（包括数据库生成的ID和时间戳等）。
     * </p>
     * @param quantityConfig 待保存或更新的分量配置数据。
     * @return 操作完成后，从数据库获取并返回的最新分量配置对象。
     */
    QuantityConfigParam saveOrUpdateQuantityConfig(QuantityConfigParam quantityConfig);

    // 把分量配置推送到项目B
    void pushTomallB();
}
