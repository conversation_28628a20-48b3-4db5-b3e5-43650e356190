package com.mall.project.service.ccreditEvolve.impl;


import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.ccreditEvolve.CcreditEvolveDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.ccreditEvolve.CcreditEvolveService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CcreditEvolveServiceImpl implements CcreditEvolveService {

    @Autowired
    private CcreditEvolveDao ccreditEvolveDao;

    @Override
    public void updateCreditEvolve() {
        ccreditEvolveDao.updateCreditEvolve();
    }

    @Override
    public CommonPage<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = ccreditEvolveDao.queryCreditEvolvePages(phone, startDate, endDate, pageSize, offset, isGreaterThanZero);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = ccreditEvolveDao.totalCreditEvolve(phone, startDate, endDate, isGreaterThanZero);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        CcreditEvolveServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new CcreditEvolveServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalCreditEvolve", todayTotalCreditEvolve(phone,startDate));   //今日信用值进化量
        summary.put("totalCreditEvolve", totalCreditEvolve(phone,startDate));   //累计信用值进化量
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    @Override
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone, String startDate, String endDate) {
        if(startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if(endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return ccreditEvolveDao.exportCreditEvolveExcel(phone,startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    @Override
    public String todayTotalCreditEvolve(String phone, String startDate) {
        return ccreditEvolveDao.todayTotalCreditEvolve(phone,startDate) == null ? "0" : ccreditEvolveDao.todayTotalCreditEvolve(phone,startDate);
    }

    @Override
    public String totalCreditEvolve(String phone, String startDate) {
        return ccreditEvolveDao.totalCreditEvolve(phone,startDate) == null ? "0" : ccreditEvolveDao.totalCreditEvolve(phone,startDate);
    }
}
