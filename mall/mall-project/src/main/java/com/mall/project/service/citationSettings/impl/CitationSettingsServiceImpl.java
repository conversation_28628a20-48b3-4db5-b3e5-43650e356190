package com.mall.project.service.citationSettings.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.citationSettings.CitationSettingsDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.citationSettings.CitationSettingsService;
import com.mall.project.service.quantityconfig.retry.PushRetryService;
import com.mall.project.util.MallBAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 引流设置服务实现类
 */
@Service
@Slf4j
public class CitationSettingsServiceImpl implements CitationSettingsService {

    @Autowired
    private CitationSettingsDao citationSettingsDao;
    
    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    @Autowired
    private PushRetryService pushRetryService;

    // 引流设置 新增
    public int addCitationSettings(String isEnabled, String citationValue ,int updatePerson) {
        // 分别对各个参数进行相应的验证
        if (!isEnabled.isEmpty() && !isEnabled.matches("^[01]$")) {
            throw new RuntimeException("引流设置开关只能为0或1");
        }
        if (!citationValue.isEmpty() && !citationValue.matches("^\\d+(\\.\\d{1,2})?$")) {
            throw new RuntimeException("引流设置单次引流值只能为正整数或小数，且最多保留两位小数");
        }
        int result = citationSettingsDao.addCitationSettings(isEnabled, citationValue, updatePerson);
        if (result > 0) {
            return 1;
        }else{
            return 0;
        }
    }

    // 引流设置 查询
    public Map<String, Object> getCitationSettings() {
        Map<String, Object> citationSettings = citationSettingsDao.getCitationSettings();
        if(citationSettings.isEmpty()){
            throw new BusinessException("暂无数据");
        }
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.convertToCamelCase(citationSettings);
    }
    // 推送引流设置到mallB系统
    @Override
    public void pushTomallB() {
        Map<String, Object> citationSettings = citationSettingsDao.getCitationSettings();
        String isEnabled = citationSettings.get("is_enabled").toString() ;
        String citationValue = citationSettings.get("citation_value").toString();
        try {
            ObjectMapper objectMapper = new ObjectMapper();

            // 构建符合mallB要求的请求体格式
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("type", 2);   //数据类似 1：分量配置，2：引流配置

            // 创建jsonObject内部数据
            Map<String, String> jsonObject = new HashMap<>();
            jsonObject.put("isEnabled", isEnabled != null ? isEnabled : "");
            jsonObject.put("citationValue", citationValue != null ? citationValue : "");

            requestMap.put("jsonObject", jsonObject);
            
            log.info("推送到mallB的数据: {}", objectMapper.writeValueAsString(requestMap));

            // 直接使用工具类发送POST请求
            ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/receiveData", requestMap, String.class);

            log.info("推送响应状态: {}", configResponse.getStatusCode());
            log.info("推送响应内容: {}", configResponse.getBody());

            if (configResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("推送引流配置到mallB系统失败: " + configResponse.getStatusCode());
            }

            // 解析响应
            if (configResponse.getBody() != null) {
                JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                int code = responseNode.path("code").asInt();
                String msg = responseNode.path("msg").asText();

                if (code != 200) {
                    throw new BusinessException("推送引流配置到mallB系统失败: " + msg);
                }

                System.out.println("推送引流配置到mallB系统成功: " + msg);
            }

        } catch (Exception e) {
            log.error("推送引流配置到mallB系统失败: {}", e.getMessage(), e);
            // 启动重试，推送类型5表示引流配置
            pushRetryService.startPushRetry(5);

            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            } else {
                throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
            }
        }
    }
}
