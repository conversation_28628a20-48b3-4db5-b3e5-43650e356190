package com.mall.project.controller.systemDatas;

import com.mall.common.api.CommonResult;
import com.mall.project.service.systemDatas.SystemDatasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 系统数据控制器
 */
@RestController
public class SystemDatasController {
    @Autowired
    private SystemDatasService systemDatasService;

    /**
     * 查询系统数据
     */
    @GetMapping("/querySystemDatas")
    public CommonResult<Map<String, Object>> querySystemDatas() {
        Map<String, Object> dataMap = systemDatasService.querySystemDatas();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("暂无数据！");
        }
    }
}
