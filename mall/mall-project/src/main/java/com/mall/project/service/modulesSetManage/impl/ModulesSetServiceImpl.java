package com.mall.project.service.modulesSetManage.impl;

import com.mall.project.dao.modulesSetManage.ModulesSetDao;
import com.mall.project.dto.modulesSetManage.ModulesParam;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.modulesSetManage.ModulesSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 功能模块服务实现类
 */
@Service
public class ModulesSetServiceImpl implements ModulesSetService {
    
    @Autowired
    private ModulesSetDao modulesMapper;
    
    @Override
    public List<ModulesParam> listAll() {
        return modulesMapper.selectAll();
    }
    
    @Override
    public ModulesParam getById(Integer id) {
        return modulesMapper.selectById(id);
    }
    
    @Override
    @Transactional
    public boolean create(ModulesParam modules) {
        // 确保级别和父模块ID的一致性
        int level = modules.getLevel();
        Integer parentId = modules.getParentId();
        if (level == 1) {
            modules.setParentId(0);
        } else if (parentId == null || parentId == 0) {
            throw new BusinessException("非一级模块必须指定父模块");
        }
        
       // 处理排序：如果前端未指定、<=0或大于最大排序+1，则默认排在末尾；否则插入到指定位置
        Integer sortOrder = modules.getSortOrder();
        List<ModulesParam> siblings = modulesMapper.selectByParentId(modules.getParentId());
        int maxOrder = siblings.isEmpty() ? 0 : siblings.get(siblings.size() - 1).getSortOrder();
        if (sortOrder == null || sortOrder <= 0 || sortOrder > maxOrder + 1) {
            modules.setSortOrder(maxOrder + 1);
        } else {
            // 在指定位置插入：后续模块排序+1
            modulesMapper.shiftSortOrderOnCreate(level, modules.getParentId(), sortOrder);
            modules.setSortOrder(sortOrder);
        }
        
        // 新增模块时，自动生成模块代码
        if (modules.getModuleCode() == null || modules.getModuleCode().isEmpty()) {
            String moduleCode = generateModuleCode(level, modules.getParentId(), null);
            modules.setModuleCode(moduleCode);
        } else {
            // 验证模块代码是否可用
            if (!isModuleCodeAvailable(modules.getModuleCode(), null)) {
                throw new BusinessException("模块代码已存在");
            }
        }
        return modulesMapper.insert(modules) > 0;
    }
    
    @Override
    @Transactional
    public boolean update(ModulesParam modules) {
        // 验证模块代码是否可用
        if (!isModuleCodeAvailable(modules.getModuleCode(), modules.getId())) {
            throw new BusinessException("模块代码已存在");
        }
        
        // 获取原始模块信息
        ModulesParam originalModule = modulesMapper.selectById(modules.getId());
        if (originalModule == null) {
            throw new BusinessException("要更新的模块不存在");
        }
        
        // 确保级别和父模块ID的一致性
        int newLevel = modules.getLevel();
        Integer newParentId = modules.getParentId();
        if (newLevel == 1) {
            modules.setParentId(0);
        } else if (newParentId == null || newParentId == 0) {
            throw new BusinessException("非一级模块必须指定父模块");
        }
        
        // 处理排序更新：原分支或跨组调整排序
        int originalOrder = originalModule.getSortOrder();
        int originalLevel = originalModule.getLevel();
        Integer originalParentId = originalModule.getParentId();
        Integer newOrder = modules.getSortOrder();
        boolean groupChanged = originalLevel != newLevel || !originalParentId.equals(newParentId);
        if (groupChanged) {
            // 从原组移除: 原组后续模块排序-1
            modulesMapper.shiftSortOrderOnDelete(originalLevel, originalParentId, originalOrder);
            // 在新组中插入: 确定新排序位置并调整后续模块
            List<ModulesParam> newSiblings = modulesMapper.selectByParentId(newParentId);
            int maxNewOrder = newSiblings.isEmpty() ? 0 : newSiblings.get(newSiblings.size() - 1).getSortOrder();
            if (newOrder == null || newOrder <= 0 || newOrder > maxNewOrder + 1) {
                modules.setSortOrder(maxNewOrder + 1);
            } else {
                modulesMapper.shiftSortOrderOnCreate(newLevel, newParentId, newOrder);
                modules.setSortOrder(newOrder);
            }
        } else {
            // 同一组内部调整
            List<ModulesParam> siblingsCurrent = modulesMapper.selectByParentId(newParentId);
            int maxCurrentOrder = siblingsCurrent.isEmpty() ? 0 : siblingsCurrent.get(siblingsCurrent.size() - 1).getSortOrder();
            int effectiveOrder;
            if (newOrder == null || newOrder <= 0) {
                effectiveOrder = originalOrder;
            } else if (newOrder > maxCurrentOrder) {
                effectiveOrder = maxCurrentOrder;
            } else {
                effectiveOrder = newOrder;
            }
            if (effectiveOrder > originalOrder) {
                modulesMapper.shiftSortOrderOnUpdateMoveDown(newLevel, newParentId, originalOrder, effectiveOrder);
            } else if (effectiveOrder < originalOrder) {
                modulesMapper.shiftSortOrderOnUpdateMoveUp(newLevel, newParentId, originalOrder, effectiveOrder);
            }
            modules.setSortOrder(effectiveOrder);
        }
        
        // 如果级别或父模块发生变化，自动生成新的模块代码
        if (originalModule.getLevel() != modules.getLevel() || 
            !originalModule.getParentId().equals(modules.getParentId())) {
            String newModuleCode = generateModuleCode(modules.getLevel(), modules.getParentId(), modules.getId());
            modules.setModuleCode(newModuleCode);
        }
        
        return modulesMapper.update(modules) > 0;
    }
    
    @Override
    @Transactional
    public boolean delete(Integer id) {
        // 获取待删除的模块信息
        ModulesParam module = modulesMapper.selectById(id);
        if (module == null) {
            throw new BusinessException("要删除的模块不存在");
        }
        int level = module.getLevel();
        Integer parentId = module.getParentId();
        int sortOrder = module.getSortOrder();
        // 删除模块
        boolean success = modulesMapper.deleteById(id) > 0;
        if (success) {
            // 删除后调整原组后续模块排序
            modulesMapper.shiftSortOrderOnDelete(level, parentId, sortOrder);
        }
        return success;
    }
    
    @Override
    public List<ModulesParam> listByParentId(Integer parentId) {
        return modulesMapper.selectByParentId(parentId);
    }

    @Override
    public String generateModuleCode(int level, Integer parentId, Integer moduleId) {
        // 如果是编辑模式，检查是否需要重新生成代码
        if (moduleId != null) {
            ModulesParam currentModule = modulesMapper.selectById(moduleId);
            if (currentModule != null) {
                // 如果级别和父模块没有变化，返回原来的代码
                if (currentModule.getLevel() == level && 
                    ((currentModule.getParentId() == 0 && parentId == 0) || 
                     (currentModule.getParentId() != null && currentModule.getParentId().equals(parentId)))) {
                    return currentModule.getModuleCode();
                }
            }
        }
        
        // 确保parentId不为null
        if (parentId == null) {
            parentId = 0;
        }
        
        if (level == 1) {
            // 一级模块：101, 102, 103...
            String maxCode = modulesMapper.getMaxModuleCodeByLevel(1);
            if (maxCode == null) {
                return "101";
            }
            try {
                int currentNum = Integer.parseInt(maxCode);
                return String.valueOf(currentNum + 1);
            } catch (NumberFormatException e) {
                throw new BusinessException("一级模块代码格式错误");
            }
        } else {
            // 必须有父模块
            if (parentId == 0) {
                //throw new BusinessException("非一级模块必须指定父模块");
            }
            
            // 获取父模块
            ModulesParam parentModule = modulesMapper.selectById(parentId);
            if (parentModule == null) {
                throw new BusinessException("父模块不存在");
            }
            
            // 验证父模块级别
            if (parentModule.getLevel() != level - 1) {
                throw new BusinessException("父模块级别不正确，应为" + (level - 1) + "级");
            }
            
            String parentCode = parentModule.getModuleCode();
            
            // 获取父模块下的同级子模块的最大代码
            List<ModulesParam> childModules = modulesMapper.selectByParentId(parentId);
            if (childModules == null || childModules.isEmpty()) {
                // 父模块下没有子模块，生成第一个子模块代码
                return parentCode + "01";
            }
            
            // 找出同级子模块中的最大代码
            int maxSuffix = 0;
            for (ModulesParam child : childModules) {
                // 跳过当前模块（编辑模式下）
                if (moduleId != null && child.getId().equals(moduleId)) {
                    continue;
                }
                
                if (child.getLevel() == level) {
                    try {
                        String childCode = child.getModuleCode();
                        if (childCode.startsWith(parentCode)) {
                            String suffix = childCode.substring(parentCode.length());
                            int suffixNum = Integer.parseInt(suffix);
                            if (suffixNum > maxSuffix) {
                                maxSuffix = suffixNum;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略格式不正确的代码
                    }
                }
            }
            
            // 生成新的子模块代码
            int nextSuffix = maxSuffix + 1;
            String formattedSuffix = nextSuffix < 10 ? "0" + nextSuffix : String.valueOf(nextSuffix);
            return parentCode + formattedSuffix;
        }
    }

    @Override
    public String getModulePath(String moduleCode) {
        if (moduleCode == null || moduleCode.isEmpty()) {
            return "";
        }

        StringBuilder path = new StringBuilder();
        String currentCode = moduleCode;
        
        while (currentCode != null && !currentCode.isEmpty()) {
            ModulesParam module = modulesMapper.selectByModuleCode(currentCode);
            if (module == null) {
                break;
            }
            
            if (path.length() > 0) {
                path.insert(0, " > ");
            }
            path.insert(0, module.getModuleCode());
            
            // 获取父模块代码
            if (module.getParentId() == 0) {
                break;
            }
            ModulesParam parentModule = modulesMapper.selectById(module.getParentId());
            currentCode = parentModule != null ? parentModule.getModuleCode() : null;
        }
        
        return path.toString();
    }

    @Override
    public boolean isModuleCodeAvailable(String moduleCode, Integer excludeId) {
        if (moduleCode == null || moduleCode.isEmpty()) {
            return false;
        }

        ModulesParam existingModule = modulesMapper.selectByModuleCode(moduleCode);
        if (existingModule == null) {
            return true;
        }
        
        // 如果是更新操作，排除当前模块
        return excludeId != null && existingModule.getId().equals(excludeId);
    }

    
    @Override
    public int generateNextSortOrder(int level, Integer parentId) {
        // 获取同一级别同父模块下所有模块的排序
        List<ModulesParam> siblings = modulesMapper.selectByParentId(parentId == null ? 0 : parentId);
        // 过滤同级别
        int maxOrder = 0;
        for (ModulesParam m : siblings) {
            if (m.getLevel() == level) {
                Integer order = m.getSortOrder();
                if (order != null && order > maxOrder) {
                    maxOrder = order;
                }
            }
        }
        return maxOrder + 1;
    }
} 