package com.mall.project.service.operationPassword;

import java.util.Map;

/**
 * 操作密码服务接口
 */
public interface OperationPasswordService {
    /**
     * 获取操作密码
     */
    public Map<String, Object> getOperationPassword(String phone);

    /**
     * 更新或修改操作密码
     */
    public int saveOrUpdateOperationPassword(String password, String phone,Integer updatePerson);

    /**
     * 验证操作密码
     */
    public boolean verifyOperationPassword(String password, String phone);
}
