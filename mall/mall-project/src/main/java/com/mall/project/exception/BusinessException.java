package com.mall.project.exception;

import lombok.Getter;

/**
 * 业务异常类
 */
public class BusinessException extends RuntimeException {
    private final String message;
    @Getter
    private int code;

    public BusinessException(String message) {
        this(message, 500);
    }

    public BusinessException(String message, int code) {
        super(message);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}