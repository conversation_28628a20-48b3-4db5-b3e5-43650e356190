package com.mall.project.controller.cQuantifyEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.cQuantifyEvolve.CQuantifyEvolve;
import com.mall.project.dto.quantifyEvolve.QuantifyEvolve;
import com.mall.project.service.cQuantifyEvolve.CQuantifyEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * C量化值进化量控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j

public class CquantifyEvolveController {

    @Autowired
    private CQuantifyEvolveService cQuantifyEvolveService;

    /**
     * 查询量化值进化量, 分页显示
     */
    @PostMapping("/queryCQuantifyEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryCQuantifyEvolvePages(@RequestBody @Valid CQuantifyEvolve param) {
        CommonPage<Map<String, Object>> commonPage = cQuantifyEvolveService.queryCQuantifyEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize(), param.getIsGreaterThanZero());
        return CommonResult.success(commonPage);
    }


    /**
     * 量化值进化量, 导出 Excel
     */
    @PostMapping("/exportCQuantifyEvolveExcel")
    public void exportCQuantifyEvolveExcel(HttpServletResponse response, @RequestBody @Valid QuantifyEvolve param) {
        try {
            // 获取量化值进化量数据
            List<Map<String, Object>> dataList = cQuantifyEvolveService.exportCQuantifyEvolveExcel(param.getPhone(), param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayCQuantifyEvolve = cQuantifyEvolveService.todayCQuantifyEvolve(param.getPhone(), param.getStartDate());  //今日C总量化值进化量
            String totalCQuantifyEvolve = cQuantifyEvolveService.totalCQuantifyEvolve(param.getPhone(), param.getStartDate());  //累计C量化值进化量

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("userName", "username");
            fieldMapping.put("quantifyEvolve", "quantifyEvolve");
            fieldMapping.put("quantifyEvolveTotal", "quantifyEvolveTotal");

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                    Arrays.asList(
                            UniversalExcelExporter.SummaryItem.builder()
                                    .name("今日量化值进化量")
                                    .value(todayCQuantifyEvolve)
                                    .build(),
                            UniversalExcelExporter.SummaryItem.builder()
                                    .name("累计量化值进化量")
                                    .value(totalCQuantifyEvolve)
                                    .build()
                    )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<QuantifyEvolve> config = UniversalExcelExporter.ExportConfig.<QuantifyEvolve>builder()
                    .dataList(dataList)
                    .entityClass(QuantifyEvolve.class)
                    .fileName("C量化值进化量")
                    .sheetName("C量化值进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出量化值进化量异常", e);
        }
    }
}
