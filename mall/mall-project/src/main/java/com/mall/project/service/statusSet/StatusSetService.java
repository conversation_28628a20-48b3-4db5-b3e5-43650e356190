package com.mall.project.service.statusSet;

import com.mall.project.dto.statusSet.StatusSet;

import java.util.Map;

/**
 * 状态设置服务接口
 */
public interface StatusSetService {

    // 保存或更新状态设置
    public Map<String, Object> saveOrUpdateStatusSet(StatusSet pojo, Integer updatePerson);

    // 查询状态设置
    public Map<String, Object> getStatusSet(String type);

    /**
     * 用户状态为不正常的用户, 当日的量化值,补贴金统计
     */
    public void updateQuantifyAndSubsidy();


    /**
     * 量化值 统计
     */
    public String quantizationValue();

    /**
     * 量化值 输出
     */
    public int outputQuantizationValue(String phone,String quantifyValue, int updatePerson);

    /**
     * 流失的 补贴金 统计
     */
    public String lostSubsidy();

    /**
     * 补贴金 输出
     */
    public int outputSubsidy(String phone,String subsidy, int updatePerson);

    /**
     * 读取企业
     */
    public Map<String, Object> queryEnterprise();

    /**
     * 查询企业是否存在手机号
     */
    public boolean checkPhoneExists(String phone, String tableName);
}
