package com.mall.project.dao.areaProportion;

import com.mall.project.dto.areaProportion.AreaProportion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 区域授权比例数据访问对象
 */
@Repository
@Slf4j
public class AreaProportionDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取区域授权比例
     */
    public Map<String, Object> getAreaProportion() {
        String sql = "SELECT lvel1_proportion,lvel2_proportion,lvel3_proportion,lvel4_proportion,on_off FROM area_proportion";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (Exception e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return Map.of();
        }
    }
    /**
     * 保存或更新区域授权比例
     */
    public int saveOrUpdateAreaProportion(AreaProportion pojo, Integer updatePerson) {
        // 当表 area_proportion 没有数据时，插入数据，有数据时，更新数据
        String sql = "INSERT INTO area_proportion(id,lvel1_proportion,lvel2_proportion,lvel3_proportion,lvel4_proportion,on_off,update_person,update_time)VALUES (1,?,?,?,?,?,?,NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "lvel1_proportion = VALUES(lvel1_proportion),lvel2_proportion = VALUES(lvel2_proportion),lvel3_proportion = VALUES(lvel3_proportion),lvel4_proportion = VALUES(lvel4_proportion),on_off = VALUES(on_off),update_person = VALUES(update_person),update_time = NOW()";
        return jdbcTemplate.update(sql, pojo.getLvel1Proportion(), pojo.getLvel2Proportion(), pojo.getLvel3Proportion(), pojo.getLvel4Proportion(), pojo.getOnOff(), updatePerson);
    }
}
