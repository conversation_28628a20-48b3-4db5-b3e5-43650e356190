package com.mall.project.controller.statusSet;


import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.statusSet.StatusSet;
import com.mall.project.service.statusSet.StatusSetService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 状态设置控制器
 */
@RestController
@RequestMapping("/api")
public class StatusSetController {

    @Autowired
    private StatusSetService statusSetService;

    /**
     * 查询状态设置
     */
    @PostMapping("/statusSet")
    public CommonResult<Map<String, Object>> statusSet(@RequestBody @Valid StatusSet pojo) {
        Map<String, Object> dataMap = statusSetService.getStatusSet(pojo.getType());
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到状态设置信息，请设置状态设置");
        }
    }

    /**
     * 保存或更新状态设置
     */
    @PutMapping("/saveOrUpdateStatusSet")
    public CommonResult<Map<String, Object>> saveOrUpdateStatusSet(@RequestBody @Valid StatusSet pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = statusSetService.saveOrUpdateStatusSet(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }

    /**
     * 量化值 统计
     */
    @GetMapping("/quantizationValue")
    public CommonResult<String> quantizationValue() {
        String quantizationValue = statusSetService.quantizationValue();
        if (isNotEmpty(quantizationValue)) {
            return CommonResult.success(quantizationValue);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 量化值 输出
     */
    @PostMapping("/outputQuantizationValue")
    public CommonResult<String> outputQuantizationValue(@RequestBody @Valid StatusSet pojo, @CurrentUser Map<String, Object> userInfo) {
        int result = statusSetService.outputQuantizationValue(pojo.getPhone(),pojo.getQuantifyValue(),Integer.parseInt(userInfo.get("id").toString()));
        if (result > 0) {
            return CommonResult.success("输出成功");
        }else{
            return CommonResult.failed("输出失败");
        }
    }

    /**
     * 流失的 补贴金 统计
     */
    @GetMapping("/lostSubsidy")
    public CommonResult<String> lostSubsidy() {
        String lostSubsidy = statusSetService.lostSubsidy();
        if (isNotEmpty(lostSubsidy)) {
            return CommonResult.success(lostSubsidy);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 补贴金 输出
     */
    @PostMapping("/outputSubsidy")
    public CommonResult<String> outputSubsidy(@RequestBody @Valid StatusSet pojo, @CurrentUser Map<String, Object> userInfo) {
        int result = statusSetService.outputSubsidy(pojo.getPhone(),pojo.getSubsidy(),Integer.parseInt(userInfo.get("id").toString()));
        if (result > 0) {
            return CommonResult.success("输出成功");
        }else{
            return CommonResult.failed("输出失败");
        }
    }

    /**
     * 读取企业
     */
    @GetMapping("/queryEnterprise")
    public CommonResult<Map<String, Object>> queryEnterprise() {
        Map<String, Object> enterprise = statusSetService.queryEnterprise();
        if (enterprise != null) {
            return CommonResult.success(enterprise);
        }else{
            return CommonResult.failed("查询失败！");
        }
    }

    /**
     * 查询企业是否存在手机号
     */
    @PostMapping("/checkPhoneExists")
    public CommonResult<String> checkPhoneExists(@RequestBody @Valid StatusSet pojo) {
        boolean result = statusSetService.checkPhoneExists(pojo.getPhone(),pojo.getTableName());
        if (result) {
            return CommonResult.success("1");
        }else{
            return CommonResult.failed("0");
        }
    }
}
