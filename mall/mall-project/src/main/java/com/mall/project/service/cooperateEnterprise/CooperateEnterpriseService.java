package com.mall.project.service.cooperateEnterprise;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 合作企业接口，定义获取所有合作企业信息的方法
 */
public interface CooperateEnterpriseService{

    /**
     * 获取所有合作企业
     */
    public Map<String, Object> getAllCooperateEnterprise();

    /**
     * 添加已设置参数的企业信息
     */
    public int addEnterpriseDataSet(Long enterpriseId);

    /**
     * 删除已设置参数的企业信息
     */
    public int deleteEnterpriseDataSet(Long enterpriseId);

    /**
     * 查看已设置参数的企业信息
     */
    public Map<String, Object> getEnterpriseDataSet();

    /**
     * 删除企业交易数据
     */
    public int deleteEnterpriseDataName(Long enterpriseProductDataId);



    /**
     * 今日总交易量
     */
    public String todayTotalTrade(int type);

    /**
     * 今日总合计数
     */
    public String todayTotalCount(int type);

    /**
     * 累计总交易量
     */
    public String cumulativeTotalTrade(int type);

    /**
     * 设置自定义常数
     */
    public int setCustomConstants(String customConstants);

    /**
     * 查询自定义常数
     */
    public Map<String, Object> getCustomConstants();

    /**
     * 添加各企业系统每日更新总累计量化数企业名称
     */
    public int addEnterpriseQuantitySet(Long enterpriseId);

    /**
     * 删除已添加各企业系统每日更新总累计量化数企业名称
     */
    public int deleteEnterpriseQuantitySet(Long enterpriseId);

    /**
     * 查看已添加各企业系统每日更新总累计量化数企业名称
     */
    public Map<String, Object> getEnterpriseQuantitySet();

    /**
     * 删除各企业系统每日更新总累计量化数交易数据
     */
    public int deleteEnterpriseQuantityName(Long enterpriseProductDataId);

    /**
     * 计算量化率 quantizationRate
     */
    public Map<String,Object> quantizationRate(String searchMonth);

    /**
     * 数据名称新增
     */
    public int tradeDataSet(Long enterpriseId, String tradeName);

    /**
     * 查询数据名称
     */
    public Map<String, Object> tradeDataSet();

    /**
     * 删除数据名称
     */
    public int deleteTradeDataSet(Long id);

    /**
     * 设置交易数据的每笔交易金额
     */
    public int tradeDataParameterSet(Long enterpriseId, String perTradeAmount,String onOff);

    /**
     * 查询设置交易数据的每笔交易金额
     */
    public Map<String, Object> QueryTradeDataParameterSet();
    /**
     * 查询中南惠 交易数据明细
     */
    CommonPage<Map<String, Object>> QueryZNHTradeDataPages(String phone, String startTime, String endTime, int pageNum, int pageSize);

    /**
     * 去mallB 系统读取每日的交易数据
     */
    public void getTradeDataFromMallB();

    /**
     * 导出 累计交易数据 Excel
     */
    List<Map<String, Object>> zNHTradeDataExport(String phone, String startTime);

    /**
     * 获取统计今日总交易量
     */
    public double sumTradeAmount(String phone, String startTime);

    /**
     * 统计中南惠今日总合计数
     */
    public double sumTotalCount(String phone, String startTime);

    /**
     * 统计每天的企业交易
     */
    public void updateEnterpriseTradeData();

    /**
     * 企业交易数据读取, 开、关
     */
    public int setEnterpriseDataSwitch(String enterpriseDataSwitch);

    /**
     * 各企业系统每日更新总累计量化数 开关
     */
    public int setQuantityDataSwitch(String quantityDataSwitch);

    /**
     * 自定义常数开、关
     */
    public int setCustomConstantsSwitch(String customConstantsSwitch);
}
