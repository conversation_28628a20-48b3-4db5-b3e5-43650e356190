package com.mall.project.dao.writeOffData;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 核销数据数据访问对象
 */
@Repository
public class WriteOffDataDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询核销数据,分页显示
     */
    public List<Map<String, Object>> queryWriteOffDataPages(String phone, String startDate, String endDate, int limit, int offset, Integer isGreaterThanZero) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy,promotion_used,total_promotion_used,write_off_gold FROM write_off_data WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        // 1. 今日核销补贴金  2. 累计核销补贴金   3. 未核销  4. 已核销促销金   5. 累计使用金额    6. 核销值
        if (isGreaterThanZero != null && isGreaterThanZero == 1) {
            sql += " AND write_off_subsidy > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
            sql += " AND write_off_subsidy_total > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 3) {
            sql += " AND un_write_off_subsidy > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 4) {
            sql += " AND promotion_used > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 5) {
            sql += " AND total_promotion_used > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 6) {
            sql += " AND write_off_gold > 0";
        }
        sql += " ORDER BY update_date DESC, id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 查询核销数据总条数
     */
    public int totalWriteOffData(String phone, String startDate, String endDate, Integer isGreaterThanZero) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM write_off_data WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        // 1. 今日核销补贴金  2. 累计核销补贴金   3. 未核销  4. 已核销促销金   5. 累计使用金额    6. 核销值
        if (isGreaterThanZero != null && isGreaterThanZero == 1) {
            sql += " AND write_off_subsidy > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 2) {
            sql += " AND write_off_subsidy_total > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 3) {
            sql += " AND un_write_off_subsidy > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 4) {
            sql += " AND promotion_used > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 5) {
            sql += " AND total_promotion_used > 0";
        }else if (isGreaterThanZero != null && isGreaterThanZero == 6) {
            sql += " AND write_off_gold > 0";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 导出核销数据 Excel
     */
    public List<Map<String, Object>> exportWriteOffDataExcel(String phone,String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,write_off_subsidy,write_off_subsidy_total,un_write_off_subsidy,promotion_used,total_promotion_used,write_off_gold FROM write_off_data WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        sql += " ORDER BY update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 统计 今日核销补贴金
     */
    public String todayTotalWriteOffSubsidy(String phone,String startDate){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT sum(write_off_subsidy) as today_total_write_off_subsidy FROM write_off_data WHERE phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date = CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 累计已核销补贴金
     */
    public String totalWriteOffSubsidy(String phone,String startDate){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(write_off_subsidy_total) AS total_write_off_subsidy\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        write_off_subsidy_total,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_date DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM write_off_data Where phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date = CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 累计未核销补贴金
     */
    public String totalUnWriteOffSubsidy(String phone,String startDate){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(un_write_off_subsidy) AS total_un_write_off_subsidy\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        un_write_off_subsidy,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_date DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM write_off_data Where phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 今日已核销促销金
     */
    public String todayPromotionUsed(String phone,String startDate){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(promotion_used) AS today_total_promotion_used FROM write_off_data WHERE phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        };
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date = CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 总累计使用金额
     */
    public String totalPromotionUsed(String phone,String startDate){
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(total_promotion_used) AS total_promotion_used\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        total_promotion_used,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_date DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM write_off_data Where phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 统计 累计核销金
     */
    public String totalWriteOffGold(String phone,String startDate) {
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(write_off_gold) AS total_write_off_gold \n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        write_off_gold,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_date DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM write_off_data Where phone <> ?";
        params.add(adminPhone);
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }
    /**
     * 保存核销数据 - 如果该手机号当天已有数据则更新，否则插入新数据
     */
    public void saveWriteOffData(String updateDate, String phone, String writeOffSubsidy, String writeOffSubsidyTotal, String unWriteOffSubsidy){
        // 先检查该手机号当天是否已有数据
        String checkSql = "SELECT COUNT(*) FROM write_off_data WHERE phone = ? AND update_date = CURDATE() ";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone);

        if (count > 0) {
            // 如果已存在，则更新数据
            String updateSql = "UPDATE write_off_data SET write_off_subsidy = ?, write_off_subsidy_total = ?, un_write_off_subsidy = ? " +
                              "WHERE phone = ? AND update_date = CURDATE()";
            jdbcTemplate.update(updateSql, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy, phone);
        } else {
            // 如果不存在，则插入新数据
            String insertSql = "INSERT INTO write_off_data(update_date, phone, write_off_subsidy, write_off_subsidy_total, un_write_off_subsidy) " +
                              "VALUES (CURDATE(), ?, ?, ?, ?)";
            jdbcTemplate.update(insertSql, phone, writeOffSubsidy, writeOffSubsidyTotal, unWriteOffSubsidy);
        }
    }

    /**
     * 保存促销金数据 - 如果该手机号当天已有数据则更新，否则插入新数据
     */
    public void savePromotionData(String phone, String promotionUsed, String totalPromotionUsed) {
        // 先检查该手机号当天是否已有数据
        String checkSql = "SELECT COUNT(*) FROM write_off_data WHERE phone = ? AND update_date = CURDATE() ";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone);

        if (count > 0) {
            // 如果已存在，则更新数据
            String updateSql = "UPDATE write_off_data SET promotion_used = ?, total_promotion_used = ? " +
                    "WHERE phone = ? AND update_date = CURDATE()";
            jdbcTemplate.update(updateSql, promotionUsed, totalPromotionUsed, phone);
        } else {
            // 如果不存在，则插入新数据
            String insertSql = "INSERT INTO write_off_data(update_date, phone, promotion_used, total_promotion_used) " +
                    "VALUES (CURDATE(), ?, ?, ?)";
            jdbcTemplate.update(insertSql, phone, promotionUsed, totalPromotionUsed);
        }
    }

    /**
     * 保存核销值数据 - 如果该手机号当天已有数据则更新，否则插入新数据
     */
    public void saveWriteOffGold(String phone, String writeOffGold) {
        // 先检查该手机号当天是否已有数据
        String checkSql = "SELECT COUNT(*) FROM write_off_data WHERE phone = ? AND update_date = CURDATE() ";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone);

        if (count > 0) {
            // 如果已存在，则更新数据
            String updateSql = "UPDATE write_off_data SET write_off_gold = ? " +
                    "WHERE phone = ? AND update_date = CURDATE()";
            jdbcTemplate.update(updateSql, writeOffGold, phone);
        } else {
            // 如果不存在，则插入新数据
            String insertSql = "INSERT INTO write_off_data(update_date, phone, write_off_gold) " +
                    "VALUES (CURDATE(), ?, ?)";
            jdbcTemplate.update(insertSql, phone, writeOffGold);
        }
    }
}
