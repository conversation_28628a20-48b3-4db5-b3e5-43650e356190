package com.mall.project.dao.modulesSetManage;


import com.mall.project.dto.modulesSetManage.ModulesParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 功能模块数据访问层
 */
@Repository
public class ModulesSetDao {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private final RowMapper<ModulesParam> rowMapper = new RowMapper<ModulesParam>() {
        @Override
        public ModulesParam mapRow(ResultSet rs, int rowNum) throws SQLException {
            ModulesParam modules = new ModulesParam();
            modules.setId(rs.getInt("id"));
            modules.setModuleName(rs.getString("module_name"));
            modules.setModuleCode(rs.getString("module_code"));
            modules.setParentId(rs.getInt("parent_id"));
            modules.setModuleUrl(rs.getString("module_url"));
            modules.setIcon(rs.getString("icon"));
            modules.setSortOrder(rs.getInt("sort_order"));
            modules.setStatus(rs.getInt("status"));
            modules.setLevel(rs.getInt("level"));
            modules.setCreateTime(rs.getTimestamp("create_time"));
            modules.setUpdateTime(rs.getTimestamp("update_time"));
            return modules;
        }
    };
    
    /**
     * 查询所有模块
     */
    public List<ModulesParam> selectAll() {
        String sql = "SELECT * FROM modules ORDER BY level, sort_order";
        return jdbcTemplate.query(sql, rowMapper);
    }
    
    /**
     * 根据ID查询模块
     */
    public ModulesParam selectById(Integer id) {
        String sql = "SELECT * FROM modules WHERE id = ?";
        List<ModulesParam> modules = jdbcTemplate.query(sql, rowMapper, id);
        return modules.isEmpty() ? null : modules.get(0);
    }
    
    /**
     * 根据模块代码查询模块
     */
    public ModulesParam selectByModuleCode(String moduleCode) {
        String sql = "SELECT * FROM modules WHERE module_code = ?";
        List<ModulesParam> modules = jdbcTemplate.query(sql, rowMapper, moduleCode);
        return modules.isEmpty() ? null : modules.get(0);
    }
    
    /**
     * 新增模块
     */
    public int insert(ModulesParam modules) {
        String sql = "INSERT INTO modules (module_name, module_code, parent_id, module_url, icon, sort_order, status, level, create_time, update_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        return jdbcTemplate.update(sql,
                modules.getModuleName(),
                modules.getModuleCode(),
                modules.getParentId(),
                modules.getModuleUrl(),
                modules.getIcon(),
                modules.getSortOrder(),
                modules.getStatus(),
                modules.getLevel());
    }
    
    /**
     * 更新模块
     */
    public int update(ModulesParam modules) {
        String sql = "UPDATE modules SET module_name = ?, module_code = ?, parent_id = ?, module_url = ?, " +
                    "icon = ?, sort_order = ?, status = ?, level = ?, update_time = NOW() WHERE id = ?";
        return jdbcTemplate.update(sql,
                modules.getModuleName(),
                modules.getModuleCode(),
                modules.getParentId(),
                modules.getModuleUrl(),
                modules.getIcon(),
                modules.getSortOrder(),
                modules.getStatus(),
                modules.getLevel(),
                modules.getId());
    }
    
    /**
     * 删除模块
     */
    public int deleteById(Integer id) {
        String sql = "DELETE FROM modules WHERE id = ?";
        return jdbcTemplate.update(sql, id);
    }
    
    /**
     * 查询父模块下的子模块列表
     */
    public List<ModulesParam> selectByParentId(Integer parentId) {
        String sql = "SELECT * FROM modules WHERE parent_id = ? ORDER BY sort_order";
        return jdbcTemplate.query(sql, rowMapper, parentId);
    }
    
    /**
     * 获取指定级别的最大模块代码
     */
    public String getMaxModuleCodeByLevel(int level) {
        String sql = "SELECT MAX(module_code) FROM modules WHERE level = ?";
        return jdbcTemplate.queryForObject(sql, String.class, level);
    }

    public int shiftSortOrderOnCreate(int level, int parentId, int startOrder) {
        String sql = "UPDATE modules SET sort_order = sort_order + 1 WHERE level = ? AND parent_id = ? AND sort_order >= ?";
        return jdbcTemplate.update(sql, level, parentId, startOrder);
    }

    public int shiftSortOrderOnUpdateMoveDown(int level, int parentId, int oldOrder, int newOrder) {
        String sql = "UPDATE modules SET sort_order = sort_order - 1 WHERE level = ? AND parent_id = ? AND sort_order > ? AND sort_order <= ?";
        return jdbcTemplate.update(sql, level, parentId, oldOrder, newOrder);
    }

    public int shiftSortOrderOnUpdateMoveUp(int level, int parentId, int oldOrder, int newOrder) {
        String sql = "UPDATE modules SET sort_order = sort_order + 1 WHERE level = ? AND parent_id = ? AND sort_order >= ? AND sort_order < ?";
        return jdbcTemplate.update(sql, level, parentId, newOrder, oldOrder);
    }

    // 新增: 当模块从原组移除时，将原组后续模块排序-1
    public int shiftSortOrderOnDelete(int level, int parentId, int order) {
        String sql = "UPDATE modules SET sort_order = sort_order - 1 WHERE level = ? AND parent_id = ? AND sort_order > ?";
        return jdbcTemplate.update(sql, level, parentId, order);
    }
} 