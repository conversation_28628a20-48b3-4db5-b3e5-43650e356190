package com.mall.project.service.creditEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 信用值进化量服务接口
 */
public interface CreditEvolveService {

    /**
     * 计算信用值进化量
     */
    public void updateCreditEvolve();

    /**
     * 查询信用值进化量,分页显示
     */
    public CommonPage<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero);

    /**
     * 信用值进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone,String startDate, String endDate);

    /**
     * 今日总量化进化量
     */
    public String todayTotalCreditEvolve(String phone, String startDate);

    /**
     * 累计量化进化量
     */
    public String totalCreditEvolve(String phone, String startDate);

    /**
     * 今日Admin量化进化量
     */
    public String todayAdminCreditEvolve(String startDate);

    /**
     * 累计Admin量化进化量
     */
    public String totalAdminCreditEvolve(String startDate);

    /**
     * 去mallB系统读取用户类型为B的信用值进化量
     */
    public void getCreditEvolveFromMallB();

    /**
     * 去mallB 读取 Admin量化进化量  数据
     */
    public void getAdminCreditEvolveFromMallB();
}
