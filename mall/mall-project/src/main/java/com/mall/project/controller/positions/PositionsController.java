package com.mall.project.controller.positions;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.position.PositionParam;
import com.mall.project.service.positions.PositionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 岗位控制器
 */
@RestController
@RequestMapping("/positions")
public class PositionsController {

    @Autowired
    private PositionsService positionsService;

    /**
     *  获取所有的岗位信息
     */
    @GetMapping("/getPositions")
    public CommonResult<Map<String, Object>> getPositions() {
        Map<String, Object> postions = positionsService.getPositions();
        if (postions != null) {
            return CommonResult.success(postions);
        } else {
            return CommonResult.failed("获取岗位信息失败");
        }
    }


    /**
     *  获取分页的岗位信息
     */
    @PostMapping("/listPaged")
    public CommonResult<CommonPage<Map<String, Object>>> getPositionsPaged(@RequestBody PositionParam request) {
        String searchName = request.getPositionName();
        int pageNum = request.getPageNumOrDefault();
        int pageSize = request.getPageSizeOrDefault();
        CommonPage<Map<String, Object>> commonPage = positionsService.getPositionsPaged(searchName, pageNum, pageSize);
        return CommonResult.success(commonPage);
    }
} 