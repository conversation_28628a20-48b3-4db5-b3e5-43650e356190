package com.mall.project.service.areaAuthorize;

import com.mall.common.api.CommonPage;
import com.mall.project.dto.areaAuthorize.AreaAuthorize;

import java.util.Map;

/**
 * 区域授权服务
 */
public interface AreaAuthorizeService {
    /**
     * 获取区域授权信息
     */
    Map<String, Object> getArea(String areaId);

    /**
     * 对手机号授权
     */
    int saveAreaAuthorize(AreaAuthorize pojo, int updatePerson);


    /**
     * 获取合作企业
     */
    Map<String, Object> getCooperateEnterprise();

    /**
     * 查询授权信息
     */
    CommonPage<Map<String, Object>> queryAreaAuthorize(String type,String level, int pageNum, int pageSize);
     /**
     * 删除授权信息, 根据表id 删除
     */
    int deleteAreaAuthorize(Integer id);


    /**
     * C授权 计算方式,这里只计算C授权的计算方式,B授权方式计算方式在QuantifyCountDao 类中 updateQuantifyCount() 方法中实现了
     * 计算C的补贴金
     * 用于定时器 执行
     */
    void updateCSubsidy();

    /**
     * 从省、市、县、乡镇 一级一级往下找代理的手机号
     */
    String findAuthorizedPhoneFromHightToLow(String areaId);

    /**
     * 乡镇、县、市、省 一级一级往上找代理的手机号
     */
    String findAuthorizedPhoneFromLowToHigh(String areaId);

    /**
     * 删除area_authorize表中授权过期的记录: end_time < NOW()
     */
    void deleteExpiredAuthorize();
}
