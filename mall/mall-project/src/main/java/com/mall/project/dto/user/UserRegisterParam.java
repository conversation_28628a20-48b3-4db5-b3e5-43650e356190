package com.mall.project.dto.user;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.mall.common.model.ExportModel;
import com.mall.project.config.StringArrayDeserializer;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户注册参数
 */
@Data
public class UserRegisterParam extends ExportModel {

    @ExcelProperty("员工ID")
    private Long employeeId;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Size(min = 11, max = 11, message = "手机号长度必须为11位")
    @ExcelProperty("手机号")
    private String phone;

    @Pattern(regexp = "^(?!^\\d+$)[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "职员名称不能为纯数字且不能包含特殊字符")
    @ExcelProperty("员工姓名")
    private String employeeName;

    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    @ExcelIgnore  //Excel导出时忽略此字段
    private String password;

    @ExcelProperty("工号")
    private String employeeNo;

    @ExcelProperty("岗位")
    private String positionName;

    @ExcelIgnore  //Excel导出时忽略此字段
    private Long positionId;

    //模块ID 两种方法 (1)标准JSON数组格式："modulesIds":["1","2","3","4"]   (2)"modulesIds":"1,2,3,4"
    @JsonDeserialize(using = StringArrayDeserializer.class)
    @ExcelIgnore  //Excel导出时忽略此字段
    private String[] modulesIds;

    private int loginLimit;

    @ExcelProperty("状态")
    @ExcelIgnore       //Excel导出时忽略此字段
    private String statusText;
}