package com.mall.project.util;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.*;
import org.springframework.stereotype.Component;

/**
 * 腾讯云短信发送工具类
 */
@Component
public class TencentSmsSender {
    
    private static final String SECRET_ID = "AKIDFZktto4IJtQoyuhXZSCk6MVXQA0slZPs";
    private static final String SECRET_KEY = "PbcPyKVSBh05xb1MUP7f082jCf2EKGzY";
    private static final String APP_ID = "1400987731"; // 你的短信应用SDK AppID
    private static final String SIGN_NAME = "广州中业科技"; // 短信签名
    private static final String TEMPLATE_ID = "2428343"; // 短信模板ID

    public static void sendSingleSms(String phoneNumber, String[] templateParams) {
        try {
            // 实例化认证对象
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);
            
            // 实例化HTTP选项
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("sms.tencentcloudapi.com"); // 短信API域名
            
            // 实例化客户端配置
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            
            // 实例化SMS客户端
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);
            
            // 实例化请求对象
            SendSmsRequest req = new SendSmsRequest();
            req.setSmsSdkAppId(APP_ID);
            req.setSignName(SIGN_NAME);
            req.setTemplateId(TEMPLATE_ID);
            req.setPhoneNumberSet(new String[] { phoneNumber }); // 号码格式:+86手机号
            req.setTemplateParamSet(templateParams); // 模板参数
            
            // 发送请求
            SendSmsResponse resp = client.SendSms(req);
            
            // 输出结果
            System.out.println(SendSmsResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.err.println("发送短信失败: " + e.toString());
        }
    }

    public static void main(String[] args) {
        // 示例：发送验证码
        String[] params = {"58987"};
        sendSingleSms("17776312351", params);
    }
}