package com.mall.project.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 二维码工具类
 */
@Slf4j
@Component
public class QRCodeUtil {

    /**
     * 生成二维码图片并保存到指定目录
     *
     * @param content 二维码内容
     * @param width   图片宽度
     * @param height  图片高度
     * @return 生成的图片文件名
     * @throws IOException     IO异常
     * @throws WriterException 生成二维码异常
     */
    public String generateQRCode(String content, int width, int height) throws IOException, WriterException {
        // 生成随机文件名
        String fileName = UUID.randomUUID() + ".jpg";
        
        // 获取用户目录 - 这是项目启动的根目录
        String userDir = System.getProperty("user.dir");
        log.info("当前工作目录: {}", userDir);
        
        // 判断当前工作目录，确定正确的路径
        String projectDir;
        if (userDir.endsWith("mall")) {
            // 如果在父项目目录，则添加子模块名称
            projectDir = userDir + "/mall-project";
        } else if (userDir.endsWith("mall-project")) {
            // 如果已经在mall-project目录，则直接使用
            projectDir = userDir;
        } else {
            // 其他情况，尝试使用相对路径
            projectDir = userDir;
            log.warn("未知工作目录: {}, 将尝试使用相对路径", userDir);
        }
        
        // 源码目录路径
        File sourceDir = new File(projectDir, "src/main/resources/static/qrcode");
        if (!sourceDir.exists()) {
            boolean created = sourceDir.mkdirs();
            if (!created) {
                log.warn("创建源码目录失败: {}", sourceDir.getAbsolutePath());
            }
        }
        
        // 创建文件
        File sourceFile = new File(sourceDir, fileName);
        log.info("二维码保存到源码路径: {}", sourceFile.getAbsolutePath());

        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.MARGIN, 2);

        // 创建二维码
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 将二维码转换为BufferedImage
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }

        // 保存图片到源码目录
        ImageIO.write(image, "jpg", sourceFile);
        
        // 同时保存到运行时目录
        try {
            // 获取运行时的classpath目录
            String classPath = ResourceUtils.getURL("classpath:").getPath();
            File runtimeDir = new File(classPath, "static/qrcode");
            if (!runtimeDir.exists()) {
                boolean created = runtimeDir.mkdirs();
                if (!created) {
                    log.warn("创建运行时目录失败: {}", runtimeDir.getAbsolutePath());
                }
            }
            
            File runtimeFile = new File(runtimeDir, fileName);
            log.info("二维码保存到运行时路径: {}", runtimeFile.getAbsolutePath());
            ImageIO.write(image, "jpg", runtimeFile);
        } catch (Exception e) {
            // 如果运行时目录保存失败，记录错误但不中断流程
            log.error("保存到运行时目录失败", e);
        }

        return fileName;
    }
} 