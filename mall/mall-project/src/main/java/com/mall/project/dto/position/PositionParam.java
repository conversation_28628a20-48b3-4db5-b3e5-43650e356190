package com.mall.project.dto.position;

import com.alibaba.excel.annotation.ExcelProperty;
import com.mall.common.model.ExportModel;
import lombok.Data;

@Data
public class PositionParam  extends ExportModel {
    @ExcelProperty("岗位ID")
    private Long id;

    @ExcelProperty("岗位名称")
    private String positionName;

    @ExcelProperty("岗位描述")
    private String description;

    @ExcelProperty("状态")
    private String statusText;

    private Integer pageNum;
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
