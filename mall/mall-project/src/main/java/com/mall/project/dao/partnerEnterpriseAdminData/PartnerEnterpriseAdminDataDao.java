package com.mall.project.dao.partnerEnterpriseAdminData;

import com.mall.project.dto.partnerEnterpriseAdminData.PartnerEnterpriseAdminData;
import com.mall.project.util.PreparedStatementUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述 : 所有合作企业Admain的各ID每日每笔数据量化数比数据访问对象
 */
@Repository
@Slf4j
public class PartnerEnterpriseAdminDataDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取所有合作企业Admain的各ID每日每笔数据量化数比数据访问对象
     */
    public Map<String,Object> getPartnerEnterpriseAdminData(){
        String sql = "SELECT is_enabled,daily_data_percentage FROM partner_enterprise_admin_data WHERE id = 1";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (EmptyResultDataAccessException e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }

    /**
     * 保存或更新所有合作企业Admain的各ID每日每笔数据量化数比数据访问对象
     */
    public int saveOrUpdatePartnerEnterpriseAdminData(PartnerEnterpriseAdminData pojo, Integer updatePerson){
        try {
            String sql = "INSERT INTO partner_enterprise_admin_data(id, is_enabled, daily_data_percentage, update_person, update_time) VALUES (1, ?, ?, ?, NOW()) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "is_enabled = VALUES(is_enabled), daily_data_percentage = VALUES(daily_data_percentage), update_time = NOW()";
            jdbcTemplate.update(connection -> {
                var ps = connection.prepareStatement(sql);
                try {
                    PreparedStatementUtil.setPreparedStatementParams(ps, pojo, updatePerson);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return ps;
            });
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }
}
