package com.mall.project.dto.user;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户登录参数
 */
@Data
public class UserLoginParam {

    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "用户名不能包含特殊字符")
    @Size(min = 2, max = 20, message = "用户名长度必须在2-20之间")
    @NotEmpty(message = "用户名不能为空")
    private String username;

    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    @NotEmpty(message = "密码不能为空")
    private String password;

    @NotEmpty(message = "token不能为空")
    private String uuid;

    @Pattern(regexp = "^\\d+$", message = "验证码只能为数字")
    @NotEmpty(message = "验证码不能为空")
    private String captcha;
}