package com.mall.project.dao.modules;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class ModulesDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取所有模块
     */
    public List<Map<String, Object>> getModules() {
        String sql = "WITH RECURSIVE full_tree AS (\n" +
                "    SELECT \n" +
                "        id,\n" +
                "        module_name,\n" +
                "        module_code,\n" +
                "        parent_id,\n" +
                "        module_url,\n" +
                "        icon,\n" +
                "        sort_order,\n" +
                "        level,\n" +
                "        CAST(LPAD(sort_order, 5, '0') AS CHAR(1000)) AS sort_path\n" +
                "    FROM modules \n" +
                "    WHERE parent_id = 0\n" +
                "    \n" +
                "    UNION ALL\n" +
                "    \n" +
                "    SELECT \n" +
                "        m.id,\n" +
                "        m.module_name,\n" +
                "        m.module_code,\n" +
                "        m.parent_id,\n" +
                "        m.module_url,\n" +
                "        m.icon,\n" +
                "        m.sort_order,\n" +
                "        m.level,\n" +
                "        CAST(CONCAT(ft.sort_path, '-', LPAD(m.sort_order, 5, '0')) AS CHAR(1000))\n" +
                "    FROM modules m\n" +
                "    INNER JOIN full_tree ft ON m.parent_id = ft.id\n" +
                "    WHERE m.status = 1\n" +
                ")\n" +
                "SELECT \n" +
                "    id,\n" +
                "    module_name,\n" +
                "    module_code,\n" +
                "    parent_id,\n" +
                "    module_url,\n" +
                "    icon,\n" +
                "    sort_order,\n" +
                "    level\n" +
                "FROM full_tree\n" +
                "ORDER BY sort_path";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 获取用户有授权的模块
     */
    public List<Map<String, Object>> getModulesByUserId(Long userId) {
        String sql = "WITH RECURSIVE full_tree AS (\n" +
                "    SELECT \n" +
                "        m.id,\n" +
                "        m.module_name,\n" +
                "        m.module_code,\n" +
                "        m.parent_id,\n" +
                "        m.module_url,\n" +
                "        m.icon,\n" +
                "        m.sort_order,\n" +
                "        1 AS level,\n" +
                "        CAST(LPAD(m.sort_order, 5, '0') AS CHAR(1000)) AS sort_path\n" +
                "    FROM modules m\n" +
                "    WHERE parent_id = 0\n" +
                "    \n" +
                "    UNION ALL\n" +
                "    \n" +
                "    SELECT \n" +
                "        m.id,\n" +
                "        m.module_name,\n" +
                "        m.module_code,\n" +
                "        m.parent_id,\n" +
                "        m.module_url,\n" +
                "        m.icon,\n" +
                "        m.sort_order,\n" +
                "        ft.level + 1 AS level,\n" +
                "        CAST(CONCAT(ft.sort_path, '-', LPAD(m.sort_order, 5, '0')) AS CHAR(1000))\n" +
                "    FROM modules m\n" +
                "    INNER JOIN full_tree ft ON m.parent_id = ft.id\n" +
                "    WHERE m.status = 1\n" +
                ")\n" +
                "SELECT \n" +
                "    ft.id,\n" +
                "    ft.module_name,\n" +
                "    ft.module_code,\n" +
                "    ft.parent_id,\n" +
                "    ft.module_url,\n" +
                "    ft.icon,\n" +
                "    ft.sort_order,\n" +
                "    ft.level\n" +
                "FROM full_tree ft\n" +
                "INNER JOIN employee_modules em  \n" +
                "    ON ft.id = em.module_id\n" +
                "WHERE em.employee_id = ?  \n" +
                "ORDER BY ft.sort_path";
        return jdbcTemplate.queryForList(sql, userId);
    }
}
