package com.mall.project.controller.citationSettings;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.citationSettings.CitationSettings;
import com.mall.project.service.citationSettings.CitationSettingsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 引流设置控制器
 */
@RestController
@RequestMapping("/citationSettings")
public class CitationSettingsController {

    @Autowired
    private CitationSettingsService citationSettingsService;

    // 引流设置 新增
    @PutMapping("/addCitationSettings")
    public CommonResult<String> addCitationSettings(@RequestBody @Valid CitationSettings citationSettings,@CurrentUser Map<String, Object> userInfo) {
        // 使用Integer.parseInt替代强制类型转换
        int result = citationSettingsService.addCitationSettings(citationSettings.getIsEnabled(), citationSettings.getCitationValue(), Integer.parseInt(userInfo.get("id").toString()));
        if (result > 0) {
            return CommonResult.success("引流设置保存成功");
        } else {
            return CommonResult.failed("引流设置保存失败");
        }
    }

    // 引流设置 查询
    @PostMapping("/getCitationSettings")
    public CommonResult<Map<String, Object>> getCitationSettings() {
        Map<String, Object> citationSettings = citationSettingsService.getCitationSettings();
        if (citationSettings != null) {
            return CommonResult.success(citationSettings);
        }else{
            return CommonResult.failed("获取引流设置信息失败");
        }
    }
}
