package com.mall.project.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * 自定义字符串数组反序列化器
 * 支持将逗号分隔的字符串转换为字符串数组
 */
public class StringArrayDeserializer extends JsonDeserializer<String[]> {

    @Override
    public String[] deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        // 如果已经是数组格式，直接返回
        if (node.isArray()) {
            String[] result = new String[node.size()];
            for (int i = 0; i < node.size(); i++) {
                result[i] = node.get(i).asText();
            }
            return result;
        }

        // 如果是字符串，尝试按逗号分隔
        if (node.isTextual()) {
            String text = node.asText();
            if (text == null || text.trim().isEmpty()) {
                return new String[0];
            }
            return text.split(",");
        }

        // 其他情况返回空数组
        return new String[0];
    }
}