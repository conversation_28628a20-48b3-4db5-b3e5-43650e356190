package com.mall.project.service.systemInfo;

import com.mall.common.api.CommonPage;

import java.util.Map;

/**
 * 系统信息服务接口
 */
public interface SystemInfoService {

    /**
     * 更新每日系统信息,这个由定时器执行,一般会在晚上12点后执行
     */
    public void updateSystemInfo();

    /**
     * 查询系统信息, 分页显示
     */
    public CommonPage<Map<String, Object>> querySystemInfoPages(String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 统计 每日数据累计量
     */
    public String dailyDataAccrueTotal(String startDate);

    /**
     * 统计 系统每日余数
     */
    public String dailyRemainderTotal(String startDate);

    /**
     * 统计 每日系统量化数总累计
     */
    public String dailyQuantityTotal(String startDate);

    /**
     * 统计 每日计量数
     */
    public String dailyMeterageTotal(String startDate);
}
