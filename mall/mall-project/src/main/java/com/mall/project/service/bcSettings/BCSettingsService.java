package com.mall.project.service.bcSettings;


import com.mall.project.dto.bcSettings.BCSettings;

import java.util.Map;

/**
 * 描述 : 合作企业各ID B/C设置 服务接口
 */
public interface BCSettingsService {

    /**
     * 查询B设置信息
     */
    public Map<String, Object> getBSettings();

    /**
     * 保存或更新B设置信息
     */
    public Map<String, Object> saveOrUpdateBSettings(BCSettings pojo, Integer updatePerson);

    /**
     * 查询C设置信息
     */
    public Map<String, Object> getCSettings();

    /**
     * 保存或更新C设置信息
     */
    public Map<String, Object> saveOrUpdateCSettings(BCSettings pojo, Integer updatePerson);
}
