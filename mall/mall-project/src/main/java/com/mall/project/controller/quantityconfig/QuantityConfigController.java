package com.mall.project.controller.quantityconfig;

import com.mall.common.api.CommonResult;
import com.mall.project.dto.quantityconfig.QuantityConfigParam;
import com.mall.project.service.quantityconfig.QuantityConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 分量配置控制器
 * <p>
 * 暴露用于管理分量配置的HTTP API接口。
 * </p>
 */
@RestController
@RequestMapping("/quantityConfig") // API路径前缀
public class QuantityConfigController {

    @Autowired
    private QuantityConfigService quantityConfigService;

    /**
     * 获取分量配置信息。
     * @return 包含分量配置的 CommonResult；如果配置未设置，则data为null。
     */
    @GetMapping("/getQuantityConfig") // Changed
    public CommonResult<QuantityConfigParam> getQuantityConfig() {
        QuantityConfigParam config = quantityConfigService.getQuantityConfig();
        if (config == null) {
            // 配置尚未设置时的友好提示
            return CommonResult.success(null, "分量配置尚未设置。");
        }
        return CommonResult.success(config);
    }

    /**
     * 保存或更新分量配置信息。
     * @param quantityConfig 从请求体中获取的分量配置数据。
     * @return 包含操作结果（成功时为保存后的配置信息）的 CommonResult。
     */
    @PostMapping("/saveOrUpdateQuantityConfig")
    public CommonResult<QuantityConfigParam> saveOrUpdateQuantityConfig(@RequestBody QuantityConfigParam quantityConfig) {
        try {
            QuantityConfigParam savedConfig = quantityConfigService.saveOrUpdateQuantityConfig(quantityConfig);
            return CommonResult.success(savedConfig, "分量配置保存成功");
        } catch (Exception e) {
            return CommonResult.failed("保存分量配置失败!"+ e.getMessage());
        }
    }
}
