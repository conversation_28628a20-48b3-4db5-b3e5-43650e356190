package com.mall.project.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务注册助手类
 * 用于查看和验证自动注册的服务
 */
@Slf4j
@Component
public class ServiceRegistrationHelper {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 获取所有已注册的Service Bean信息
     * @return 服务Bean信息映射
     */
    public Map<String, String> getAllRegisteredServices() {
        Map<String, Object> serviceBeans = applicationContext.getBeansWithAnnotation(Service.class);
        
        return serviceBeans.entrySet().stream()
            .filter(entry -> entry.getValue().getClass().getName().startsWith("com.mall.project.service"))
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getClass().getName()
            ));
    }

    /**
     * 获取项目中所有Service Bean的名称
     * @return Service Bean名称集合
     */
    public Set<String> getProjectServiceNames() {
        return getAllRegisteredServices().keySet();
    }

    /**
     * 检查指定的服务是否已注册
     * @param serviceName 服务名称
     * @return 是否已注册
     */
    public boolean isServiceRegistered(String serviceName) {
        try {
            Object bean = applicationContext.getBean(serviceName);
            return bean.getClass().getName().startsWith("com.mall.project.service");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 打印所有已注册的服务信息
     */
    public void printAllRegisteredServices() {
        Map<String, String> services = getAllRegisteredServices();
        log.info("=== 已注册的项目Service Bean ===");
        log.info("总数: {}", services.size());
        
        services.forEach((beanName, className) -> {
            log.info("Bean名称: {} -> 类名: {}", beanName, className);
        });
        
        log.info("=== 注册信息打印完成 ===");
    }

    /**
     * 验证DynamicMethodInvoker是否能够正确调用新注册的服务
     * @param dynamicMethodInvoker 动态方法调用器
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return 调用结果
     */
    public String testServiceMethod(DynamicMethodInvoker dynamicMethodInvoker, String serviceName, String methodName) {
        try {
            String methodExpression = serviceName + "." + methodName + "()";
            log.info("测试调用方法: {}", methodExpression);
            
            String result = dynamicMethodInvoker.invokeMethod(methodExpression);
            log.info("调用结果: {}", result);
            
            return result;
        } catch (Exception e) {
            log.error("测试调用失败: serviceName={}, methodName={}", serviceName, methodName, e);
            return "调用失败: " + e.getMessage();
        }
    }
}
