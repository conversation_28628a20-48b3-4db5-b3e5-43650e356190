package com.mall.project.dao.systemDatas;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 系统数据数据访问对象
 */
@Repository
@Slf4j
public class SystemDatasDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询系统数据
     */
    public Map<String, Object> querySystemDatas() {
        String sql = "SELECT sys_quantity_value,sys_credit,sys_promotion_gold,sys_quantity_num,sys_subsidy_funds,sys_verified_subsidy,sys_weight FROM system_datas WHERE DATE(update_date) = (select DATE(max(update_date)) from system_datas)";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (org.springframework.dao.EmptyResultDataAccessException e) {
            // 当查询没有返回任何结果时，返回空Map
            return java.util.Collections.emptyMap();
        } catch (Exception e) {
            // 记录其他异常信息
            log.error("查询系统数据时发生异常: {}", e.getMessage(), e);
            return java.util.Collections.emptyMap();
        }
    }

    /**
     * 更新系统数据,这个由定时器执行,一般会在凌晨1点后执行
     */
    public void updateSystemDatas(String sysQuantityValue,String sysCredit,String sysPromotionGold,String sysQuantityNum,String sysSubsidyFunds,String sysVerifiedSubsidy,String sysWeightTotal) {
          // 插入数据到system_datas 表,如果update_date 已经存在则更新,不存在则插入,使用SELECT EXISTS 判断
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM system_datas WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY)", Integer.class) == 0) {
            String sql = "INSERT INTO system_datas(sys_quantity_value,sys_credit,sys_promotion_gold,sys_quantity_num,sys_subsidy_funds,sys_verified_subsidy,sys_weight,update_date)VALUES(?, ?, ?, ?, ?, ?, ?, CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, sysQuantityValue, sysCredit, sysPromotionGold, sysQuantityNum, sysSubsidyFunds, sysVerifiedSubsidy, sysWeightTotal);
        }else{
            String sql = "UPDATE system_datas SET sys_quantity_value = ?,sys_credit = ?,sys_promotion_gold = ?,sys_quantity_num = ?,sys_subsidy_funds = ?,sys_verified_subsidy = ?,sys_weight = ? WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, sysQuantityValue, sysCredit, sysPromotionGold, sysQuantityNum, sysSubsidyFunds, sysVerifiedSubsidy, sysWeightTotal);
        }
    }

    /**
     * 系统信用值累计
     */

    /**
     * 系统促销金累计
     */

    /**
     * 系统补贴金累计
     */

    /**
     * 系统已核销补贴金累计
     */

    /**
     * 系统分量累计
     */
    public String sysWeightTotal(){
        String sql = "SELECT \n" +
                "    SUM(CAST(weight_count AS DECIMAL(20,2))) AS sys_weight_total\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        weight_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_time DESC, id DESC\n" +
                "        ) AS rnk\n" +
                "    FROM mall_b_users_count\n" +
                ") AS latest_records\n" +
                "WHERE rnk = 1";
        try {
            String result = jdbcTemplate.queryForObject(sql, String.class);
            // 处理null值情况
            return result != null ? result : "0.00";
        } catch (org.springframework.dao.EmptyResultDataAccessException e) {
            // 当查询没有返回任何结果时，返回默认值
            return "0.00";
        } catch (Exception e) {
            // 记录其他异常信息
            log.error("查询系统分量累计时发生异常: {}", e.getMessage(), e);
            return "0.00";
        }
    }
}
