package com.mall.project.controller.dailyTradePercentage;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.dailyTradePercentage.DailyTradePercentage;
import com.mall.project.service.dailyTradePercentage.DailyTradePercentageService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置 控制器
 */
@RestController
public class DailyTradePercentageController {

    @Autowired
    public DailyTradePercentageService dailyTradePercentageService;


    @GetMapping("/getDailyTradePercentage")
    public CommonResult<Map<String, Object>> getDailyTradePercentage() {
        Map<String, Object> dailyTradePercentage = dailyTradePercentageService.getDailyTradePercentage();
        if (!dailyTradePercentage.isEmpty()) {
            return CommonResult.success(dailyTradePercentage);
        }else{
            return CommonResult.failed("暂无数据！");
        }
    }

    @PutMapping("/saveOrUpdateDailyTradePercentage")
    public CommonResult<Map<String, Object>> saveOrUpdateDailyTradePercentage(@RequestBody @Valid DailyTradePercentage pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = dailyTradePercentageService.saveOrUpdateDailyTradePercentage(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (!dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }
}
