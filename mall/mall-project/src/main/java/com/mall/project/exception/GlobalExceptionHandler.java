package com.mall.project.exception;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidDefinitionException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.mall.common.api.CommonResult;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 调用时机说明
     * 参数校验失败时触发（MethodArgumentNotValidException）
     * 当 @Valid 注解校验失败时自动触发
     * 典型场景：请求参数 mobile 或 password 未传入、格式错误等
     *  Spring MVC 核心组件
     *        → 参数解析器（RequestBodyMethodProcessor）
     *          → 触发 @Valid 校验
     *            → 校验失败抛出 MethodArgumentNotValidException
     *              → 被 GlobalExceptionHandler.handleValidationExceptions 捕获
     *  数据绑定异常时触发（BindException）
     * 当请求参数类型转换失败时触发（如将字符串转为数字失败）
     * 示例场景：请求参数 age="abc" 但字段类型为 Integer
     */
    // 捕获参数校验异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        log.error("参数校验失败", ex);
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        StringBuilder errorMessage = new StringBuilder("参数传入不完整: ");
        for (FieldError error : fieldErrors) {
            errorMessage.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        return CommonResult.failed(errorMessage.toString());
    }
    
    // 捕获约束违反异常
    @ExceptionHandler(ConstraintViolationException.class)
    public CommonResult<String> handleConstraintViolationException(ConstraintViolationException ex) {
        log.error("参数约束违反", ex);
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        StringBuilder errorMessage = new StringBuilder("参数约束违反: ");
        for (ConstraintViolation<?> violation : violations) {
            errorMessage.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        return CommonResult.failed(errorMessage.toString());
    }

    // 捕获其他绑定异常（可选）
    @ExceptionHandler(BindException.class)
    public CommonResult<String> handleBindException(BindException ex) {
        log.error("参数绑定失败", ex);
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        StringBuilder errorMessage = new StringBuilder("参数绑定失败: ");
        for (FieldError error : fieldErrors) {
            errorMessage.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        return CommonResult.failed(errorMessage.toString());
    }

    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public CommonResult<String> handleHttpMessageNotReadable(HttpMessageNotReadableException ex) {
        log.error("请求参数解析失败", ex);
        String errorMessage = "请求参数格式错误";
        Throwable cause = ex.getCause();

        if (cause instanceof InvalidFormatException ife) {
            if (ife.getPath() != null && !ife.getPath().isEmpty()) {
                String fieldName = ife.getPath().get(0).getFieldName();
                Object value = ife.getValue();
                Class<?> targetType = ife.getTargetType();
                errorMessage = String.format("参数[%s]的值[%s]无法转换为目标类型[%s]",
                        fieldName, value, targetType.getSimpleName());
            }
        } else if (cause instanceof JsonMappingException jme) {
            if (jme.getPath() != null && !jme.getPath().isEmpty()) {
                String fieldName = jme.getPath().get(0).getFieldName();
                errorMessage = String.format("参数[%s]的格式不正确", fieldName);
            }
        } else if (cause instanceof JsonParseException) {
            errorMessage = "JSON格式不正确，请检查请求体格式";
        }

        return CommonResult.validateFailed(errorMessage);
    }

    /**
     * 处理HTTP消息转换异常
     */
    @ExceptionHandler(HttpMessageConversionException.class)
    public CommonResult<String> handleHttpMessageConversionException(HttpMessageConversionException ex) {
        log.error("HTTP消息转换异常", ex);
        String errorMessage = "请求参数格式错误";

        // 提取数组类型错误信息
        if (ex.getMessage().contains("array type")) {
            // 尝试提取字段名
            Pattern pattern = Pattern.compile("through reference chain: .*\\[\"(.*?)\"]");
            Matcher matcher = pattern.matcher(ex.getMessage());
            if (matcher.find()) {
                String fieldName = matcher.group(1);
                errorMessage = String.format("参数[%s]应该是数组格式，请使用[]包裹，例如: [\"%s\"]",
                        fieldName, "1\",\"2\",\"3");
            } else {
                errorMessage = "数组类型参数格式错误，请使用[]包裹元素";
            }
        }

        // 处理InvalidDefinitionException
        Throwable cause = ex.getCause();
        if (cause instanceof InvalidDefinitionException) {
            String causeMessage = cause.getMessage();
            if (causeMessage.contains("String[]") && causeMessage.contains("no String-argument constructor")) {
                Pattern pattern = Pattern.compile("through reference chain: .*\\[\"(.*?)\"]");
                Matcher matcher = pattern.matcher(causeMessage);
                if (matcher.find()) {
                    String fieldName = matcher.group(1);
                    errorMessage = String.format("参数[%s]应该是数组格式，请使用JSON数组格式，例如: [\"%s\"]而不是逗号分隔的字符串",
                            fieldName, "1\",\"2\",\"3");
                }
            }
        }

        return CommonResult.validateFailed(errorMessage);
    }

    /**
     * 处理业务异常
     *
     *
     */
    @ExceptionHandler(BusinessException.class)
    public CommonResult<String> handleBusinessException(BusinessException ex) {
        return CommonResult.failed(ex.getMessage());
    }

    /**
     * 处理所有未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<String> handleAllException(Exception ex) {
        log.error("全局异常", ex); // 日志里有完整堆栈
        // 默认只返回通用错误
        String errorMessage = "服务器内部错误";

        // 判断是否为开发环境
        String activeProfile = System.getProperty("spring.profiles.active");
        if (activeProfile != null && activeProfile.contains("dev")) {
            // 开发环境返回详细异常信息
            StringBuilder detail = new StringBuilder();
            detail.append(ex.toString()).append("\n");
            for (StackTraceElement element : ex.getStackTrace()) {
                detail.append("\tat ").append(element).append("\n");
            }
            errorMessage += ":\n" + detail;
        } else {
            // 生产环境只返回简要信息
            errorMessage += ": " + ex.getMessage();
        }
        return CommonResult.failed(errorMessage);
    }
}