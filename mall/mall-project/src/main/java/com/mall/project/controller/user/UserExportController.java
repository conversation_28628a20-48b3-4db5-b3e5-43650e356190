package com.mall.project.controller.user;

import com.mall.common.util.ExcelUtil;
import com.mall.project.dto.position.PositionParam;
import com.mall.project.dto.user.UserRegisterParam;
import com.mall.project.service.positions.PositionsService;
import com.mall.project.service.user.UserService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户导出控制器
 */
@RestController
@RequestMapping("/user/export")
@Slf4j
public class UserExportController {

    @Autowired
    private UserService userService;

    @Autowired
    private PositionsService positionsService;

    /**
     * 导出员工数据（Map方式导出）
     */
    @GetMapping("/employees/map")
    public void exportEmployeesMap(HttpServletResponse response) {
        try {
            // 获取员工数据
            List<Map<String, Object>> dataList = userService.getEmployeesForExport();
            
            // 定义表头和字段
            List<String> headList = Arrays.asList("员工ID", "员工姓名", "手机号", "工号", "岗位", "状态", "创建时间", "更新时间");
            List<String> keyList = Arrays.asList("id", "employee_name", "phone", "employee_no", "position_name", "status_text", "create_time", "update_time");

            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("create_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    data.put("create_time", formattedDateTime);
                }
            });
            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("update_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    data.put("update_time", formattedDateTime);
                }
            });
            
            // 导出Excel
            ExcelUtil.exportExcel(response, "员工数据", "员工列表", headList, keyList, dataList);
        } catch (Exception e) {
            log.error("导出员工数据异常", e);
        }
    }
    
    /**
     * 导出员工数据（POJO方式导出）
     */
    @GetMapping("/employees/pojo")
    public void exportEmployeesPojo(HttpServletResponse response) {
        try {
            // 获取员工数据
            List<Map<String, Object>> dataList = userService.getEmployeesForExport();
            
            // 将Map数据转换为POJO
            List<UserRegisterParam> exportList = convertToEmployeeExportModel(dataList);
            
            // 导出Excel
            ExcelUtil.exportExcel(response, "员工数据", "员工列表", UserRegisterParam.class, exportList);
        } catch (Exception e) {
            log.error("导出员工数据异常", e);
        }
    }

    /**
     * 导出多Sheet数据（员工数据和岗位数据）
     */
    @GetMapping("/multisheet")
    public void exportMultiSheet(HttpServletResponse response) {
        try {
            // 1. 获取员工数据并转换为模型
            List<Map<String, Object>> employeeDataList = userService.getEmployeesForExport();
            List<UserRegisterParam> employeeModels = convertToEmployeeExportModel(employeeDataList);

            // 2. 获取岗位数据并转换为模型
            List<Map<String, Object>> positionDataList = positionsService.getPositionsForExport();
            List<PositionParam> positionModels = convertToPositionExportModel(positionDataList);

            // 3. 创建Sheet配置
            List<ExcelUtil.SheetConfig<?>> sheetConfigs = new ArrayList<>();

            // 第一个sheet：员工数据
            ExcelUtil.SheetConfig<UserRegisterParam> employeeSheet =
                    new ExcelUtil.SheetConfig<>("员工列表", UserRegisterParam.class, employeeModels);
            sheetConfigs.add(employeeSheet);

            // 第二个sheet：岗位数据
            ExcelUtil.SheetConfig<PositionParam> positionSheet =
                    new ExcelUtil.SheetConfig<>("岗位列表", PositionParam.class, positionModels);
            sheetConfigs.add(positionSheet);

            // 4. 导出多Sheet Excel
            ExcelUtil.exportMultiSheetExcel(response, "员工和岗位数据", sheetConfigs);

        } catch (Exception e) {
            log.error("导出多Sheet Excel异常", e);
        }
    }
    
    /**
     * 将Map数据转换为POJO
     */
    private List<UserRegisterParam> convertToEmployeeExportModel(List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        return dataList.stream().map(map -> {
            UserRegisterParam model = new UserRegisterParam();
            model.setEmployeeId(Long.valueOf(String.valueOf(map.get("id"))));
            model.setEmployeeName(String.valueOf(map.get("employee_name")));
            model.setPhone(String.valueOf(map.get("phone")));
            model.setEmployeeNo(String.valueOf(map.get("employee_no")));
            model.setPositionName(map.get("position_name") != null ? String.valueOf(map.get("position_name")) : "");
            model.setStatusText(String.valueOf(map.get("status_text")));

            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("create_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    model.setCreateTime(formattedDateTime);
                }
            });
            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("update_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    model.setUpdateTime(formattedDateTime);
                }
            });
            
            return model;
        }).collect(Collectors.toList());
    }
    /**
     * 将Map数据转换为岗位导出模型
     */
    private List<PositionParam> convertToPositionExportModel(List<Map<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return dataList.stream().map(map -> {
            PositionParam model = new PositionParam();
            model.setId(Long.valueOf(String.valueOf(map.get("id"))));
            model.setPositionName(String.valueOf(map.get("position_name")));
            model.setDescription(map.get("description") != null ? String.valueOf(map.get("description")) : "");
            model.setStatusText(String.valueOf(map.get("status_text")));

            // 日期格式化
            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("create_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    model.setCreateTime(formattedDateTime);
                }
            });
            dataList.forEach(data -> {
                // 处理 datetime 字段
                Object dateTimeObj = data.get("update_time"); // 假设字段名为 create_time
                if (dateTimeObj instanceof LocalDateTime dateTime) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    String formattedDateTime = dateTime.format(formatter);
                    model.setUpdateTime(formattedDateTime);
                }
            });

            return model;
        }).collect(Collectors.toList());
    }
} 