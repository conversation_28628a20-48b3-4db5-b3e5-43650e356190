package com.mall.project.service.user;


import com.mall.project.dto.user.UserRegisterParam;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 */
public interface UserService {
    /**
     * 用户注册
     *
     * @param userRegisterParam 用户注册参数
     * @return 注册结果
     */
    int register(UserRegisterParam userRegisterParam);

    /**
     * 更新用户信息
     *
     * @param employeeId 职员id
     * @param employeeName 用户名
     * @param phone 手机号
     * @param password 密码
     * @param positionId 岗位id
     * @param modulesIds 模块id
     * @return 更新结果
     */
    int updateEmployeeInfo(Long employeeId, String employeeName, String phone, String password, Long positionId, String[] modulesIds);

    // 删除职员信息
    int deleteEmployeeInfo(Long employeeId);

    // 按职员名称或手机号搜索用户信息
    List<Map<String, Object>> searchEmployeeInfo(String employeeName, String phone);

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    Map<String, Object> login(String username, String password, String uuid, String captcha);

    /**
     * 用户登出
     *
     * @return 登出成功返回true，登出失败返回false
     */
    boolean logout(String token);

    Map<String, Object> getUserALLUpLevel(Long userId);

    // 限制登录开关
    int updateLoginLimit(Long employeeId, int loginLimit);

    /**
     * 导出员工数据
     * 
     * @return 员工数据列表
     */
    List<Map<String, Object>> getEmployeesForExport();

    // 根据员工ID获取员工信息: 名字,手机号,工号,岗位ID,创建时间,限制登入
    Map<String, Object> getEmployeeById(Long employeeId);

}
