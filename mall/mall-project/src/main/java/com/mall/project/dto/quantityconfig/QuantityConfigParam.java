package com.mall.project.dto.quantityconfig;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 描述：分量配置类
 */
@Data
public class QuantityConfigParam {
    /**
     * 分量配置ID
     */
    @Min(1)
    @Max(value = 9999999999L)
    private Integer id;
    /**
     * 系统初始达标分量门槛值（固定数值，如 10000）
     */
    @Pattern(regexp = "^-?\\d+$", message = "系统初始达标分量门槛值只能为整数")
    private String initialThreshold;
    /**
     * 每天需要新增分量以保持达标（例如每日新增 5000）
     */
    @Pattern(regexp = "^-?\\d+$", message = "每天需要新增分量以保持达标只能为整数")
    private String dailyThreshold;
    /**
     * 达到“每日保持达标”后，可兑换的平台金（如 50）
     */
    // 验证只能为正整数和小数,且保留两位小数
    @Pattern(regexp = "^\\d+(\\.\\d{1,2})?$", message = "达到“每日保持达标”后，可兑换的平台金只能为正整数或小数，且最多保留两位小数")
    private String dailyThresholdReward;
    /**
     * 所需推荐商家数（例：10）
     */
    @Pattern(regexp = "^-?\\d+$", message = "所需推荐商家数只能为整数")
    private String recommendMerchantsNum;
    /**
     * 达到上面推荐商家数后，可以增加的平台金上限（例：5）
     */
    @Pattern(regexp = "^\\d+(\\.\\d{1,2})?$", message = "达到上面推荐商家数后，可以增加的平台金上限只能为正整数或小数，且最多保留两位小数")
    private String recommendMerchantsReward;
    /**
     * 月度“有技术引流”商家数阈值（达到此数后触发奖励，例如 20）
     */
    @Pattern(regexp = "^-?\\d+$", message = "月度“有技术引流”商家数阈值只能为整数")
    private String monthlyTechThreshold;
    /**
     * 月度“有技术引流”达标后，增加的平台金上限（例如 10）
     */
    @Pattern(regexp = "^\\d+(\\.\\d{1,2})?$", message = "月度“有技术引流”达标后，增加的平台金上限只能为正整数或小数，且最多保留两位小数")
    private String monthlyTechRewardGold;
    /**
     * 月度“无技术引流”商家数阈值（达到此数后触发扣减，例如 5）
     */
    @Pattern(regexp = "^-?\\d+$", message = "月度“无技术引流”商家数阈值只能为整数")
    private String monthlyNoTechThreshold;
    /**
     * 月度“无技术引流”达标后，扣减的平台金数量（例如 5）
     */
    @Pattern(regexp = "^\\d+(\\.\\d{1,2})?$", message = "月度“无技术引流”达标后，扣减的平台金数量只能为正整数或小数，且最多保留两位小数")
    private String monthlyNoTechPenaltyGold;
    /**
     * 每天额外新增分量单位（例如 1000：表示每 1000 分量可兑换以下对应平台金）
     */
    @Pattern(regexp = "^-?\\d+$", message = "每天额外新增分量单位只能为整数")
    private String dailyExtraQuantityUnit;
    /**
     * 与 daily_extra_quantity_unit 配对：达到单位分量后，可兑换的平台金（例如达到 1000 分量，可兑换 10 平台金）
     */
    @Pattern(regexp = "^\\d+(\\.\\d{1,2})?$", message = "达到每天额外新增分量后，可兑换的平台金只能为正整数或小数，且最多保留两位小数")
    private String dailyExtraRewardGold;

    /**
     * 分量设置开关,0 - 开, 1 - 关
     */
    @Pattern(regexp = "^[01]$", message = "分量设置开关只能为0或1")
    private String onOff;
}
