package com.mall.project.controller.user;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.user.UserLoginParam;
import com.mall.project.dto.user.UserRegisterParam;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.user.UserService;
import com.mall.project.util.TencentSmsSender;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 用户控制器
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private TencentSmsSender tencentSmsSender;

    /**
     * 用户注册
     */
    @PostMapping("/add")
    public CommonResult<String> register(@RequestBody @Valid UserRegisterParam param) {
        int result = userService.register(param);
        if (result > 0) {
            return CommonResult.success("新增职员成功");
        } else if(result == -1) {
            return CommonResult.failed("新增职员失败，手机号已被注册");
        }  else{
            return CommonResult.failed("网络错误，请稍后重试");
        }
    }
    /**
     * 更新职员信息
     */
    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody @Valid UserRegisterParam param) {
        try {
            userService.updateEmployeeInfo(param.getEmployeeId(), param.getEmployeeName(), param.getPhone(), param.getPassword(), param.getPositionId(), param.getModulesIds());
            return CommonResult.success("更新职员信息成功");
        } catch (BusinessException e) {
            return CommonResult.failed(e.getMessage()); // 返回具体错误信息
        }
    }

    /**
     * 删除职员信息
     */
    @PostMapping("/delete")
    public CommonResult<String> delete(@RequestBody UserRegisterParam param) {
        int result = userService.deleteEmployeeInfo(param.getEmployeeId());
        if (result > 0) {
            return CommonResult.success("删除职员信息成功");
        }
        return CommonResult.failed("删除职员信息失败");
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public CommonResult<Map<String, Object>> login(@RequestBody @Valid UserLoginParam param) {
        Map<String, Object> userInfo = userService.login(param.getUsername(), param.getPassword(), param.getUuid(), param.getCaptcha());
        if (userInfo == null) {
            return CommonResult.failed("登录失败，系统错误");
        }
        Object result = userInfo.get("result");
        if (result == null) {
            return CommonResult.failed("登录失败，返回结果异常");
        }
        String resultStr = result.toString();
        return switch (resultStr) {
            case "0" -> CommonResult.validateFailed("验证码错误");
            case "1" -> CommonResult.validateFailed("用户名或密码错误");
            case "2", "3" -> {
                String msg = userInfo.get("msg") != null ? userInfo.get("msg").toString() : "未知错误";
                yield CommonResult.validateFailed(msg);
            }
            default -> {
                userInfo.remove("result");
                yield CommonResult.success(userInfo);
            }
        };
    }

    /**
     * 获取当前登录用户信息
     */
    @PostMapping("/info")
    public CommonResult<Map<String, Object>> getInfo(@CurrentUser Map<String, Object> userInfo) {
        if (userInfo != null) {
            return CommonResult.success(userInfo);
        } else {
            return CommonResult.unauthorized(null);
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public CommonResult<String> logout(String token) {
        boolean result = userService.logout(token);
        return result ? CommonResult.success("登出成功") : CommonResult.failed("登出失败");
    }

    /**
     * 获取用户关系链
     */
    @GetMapping("/getRelationshipChain")
    public CommonResult<Map<String, Object>> getUserALLUpLevel(@RequestParam Long userId) {
        Map<String, Object> relationshipChain = userService.getUserALLUpLevel(userId);
        if (relationshipChain != null) {
            return CommonResult.success(relationshipChain);
        } else {
            return CommonResult.failed("获取关系链失败");
        }
    }

    // 按职员名称或手机号搜索用户信息
    @PostMapping("/searchByNameOrPhone")
    public CommonResult<List<Map<String, Object>>> searchEmployeeInfo(UserRegisterParam param) {
        List<Map<String, Object>> userInfo = userService.searchEmployeeInfo(param.getEmployeeName(), param.getPhone());
        if (userInfo != null && !userInfo.isEmpty()) {
            return CommonResult.success(userInfo);
        } else {
            return CommonResult.failed("获取用户信息失败");
        }
    }

    // 限制登录开关
    @PostMapping("/updateLoginLimit")
    public CommonResult<String> updateLoginLimit(@RequestBody @Valid UserRegisterParam param) {
        int result = userService.updateLoginLimit(param.getEmployeeId(), param.getLoginLimit());
        if (result > 0) {
            return CommonResult.success("更新登录限制成功");
        } else {
            return CommonResult.failed("更新登录限制失败");
        }
    }

    // 根据员工ID获取员工信息: 名字,手机号,工号,岗位ID,创建时间,限制登入
    @PostMapping("/getEmployeeById")
    public CommonResult<Map<String, Object>> getEmployeeById(@RequestBody UserRegisterParam param) {
        Map<String, Object> employeeInfo = userService.getEmployeeById(param.getEmployeeId());
        if (employeeInfo != null) {
            return CommonResult.success(employeeInfo);
        } else {
            return CommonResult.failed("获取员工信息失败");
        }
    }
}
