package com.mall.project.service.modules.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.modules.ModulesDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.modules.ModulesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ModulesServiceImpl implements ModulesService {

    @Autowired
    private ModulesDao modulesDao;

    /**
     * 获取所有模块
     * @return
     */
    @Override
    public Map<String, Object> getModules() {
        List<Map<String, Object>> modules = modulesDao.getModules();
        Map<String, Object> result = new HashMap<>();
        result.put("modules", modules);
        result.put("total", modules.size());
        return result;
    }

    /**
     * 获取用户有授权的模块
     * @param employeeId
     * @return
     */
    @Override
    public Map<String, Object> getUserModules(Long employeeId) {
        if (employeeId == null) {
            throw new BusinessException("员工编号不能为空");
        }
        List<Map<String, Object>> modules = modulesDao.getModulesByUserId(employeeId);
        Map<String, Object> result = new HashMap<>();
        result.put("modules", modules);
        result.put("total", modules.size());
        return result;
    }

    /**
     * 获取用户有授权的模块, 树格结构
     */
    @Override
    public Map<String, Object> getUserModulesTree(Long userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 获取用户授权的所有模块
        List<Map<String, Object>> allModules = modulesDao.getModulesByUserId(userId);
        if (allModules == null || allModules.isEmpty()) {
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("menuList", new ArrayList<>());
            return emptyResult;
        }
        
        // 构建树形结构
        List<Map<String, Object>> menuTree = buildMenuTree(allModules);
        
        // 递归转换整个树结构为驼峰命名
        List<Map<String, Object>> camelCaseMenuTree = convertTreeToCamelCase(menuTree);
        
        Map<String, Object> result = new HashMap<>();
        result.put("menuList", camelCaseMenuTree);
        return result;
    }
    
    /**
     * 构建菜单树
     * @param allModules 所有模块列表
     * @return 树形结构的模块列表
     */
    private List<Map<String, Object>> buildMenuTree(List<Map<String, Object>> allModules) {
        // 先过滤出顶级模块（parent_id = 0）
        List<Map<String, Object>> rootModules = allModules.stream()
            .filter(module -> Integer.valueOf(0).equals(module.get("parent_id")))
            .collect(Collectors.toList());
        
        // 递归设置子模块
        rootModules.forEach(rootModule -> {
            setChildren(rootModule, allModules);
        });
        
        return rootModules;
    }
    
    /**
     * 递归设置子模块
     * @param parentModule 父模块
     * @param allModules 所有模块列表
     */
    private void setChildren(Map<String, Object> parentModule, List<Map<String, Object>> allModules) {
        // 找出当前模块的所有子模块
        List<Map<String, Object>> childrenModules = allModules.stream()
            .filter(module -> parentModule.get("id").equals(module.get("parent_id")))
            .collect(Collectors.toList());

//        if (!childrenModules.isEmpty()) {
            // 递归处理每个子模块
            childrenModules.forEach(child -> {
                setChildren(child, allModules);
            });
            
            // 设置子模块到父模块的children属性
            parentModule.put("children", childrenModules);
//        }
    }

    /**
     * 递归转换树形结构为驼峰命名格式
     * @param treeNodes 树节点列表
     * @return 驼峰命名格式的树节点列表
     */
    private List<Map<String, Object>> convertTreeToCamelCase(List<Map<String, Object>> treeNodes) {
        if (treeNodes == null || treeNodes.isEmpty()) {
            return treeNodes;
        }
        
        return treeNodes.stream().map(node -> {
            // 先转换当前节点
            Map<String, Object> camelCaseNode = ConvertToCamelCase.convertToCamelCase(node);
            
            // 处理子节点
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("children");
            if (children != null && !children.isEmpty()) {
                // 递归转换子节点
                camelCaseNode.put("children", convertTreeToCamelCase(children));
            }
            
            return camelCaseNode;
        }).collect(Collectors.toList());
    }
}
