package com.mall.project.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态方法调用器
 * 使用Spring表达式语言（SpEL）动态调用服务方法
 */
@Slf4j
@Component
public class DynamicMethodInvoker {

    @Autowired
    private ApplicationContext applicationContext;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final ConcurrentHashMap<String, Expression> expressionCache = new ConcurrentHashMap<>();

    // 缓存已注册的服务名称，避免重复注册
    private final Set<String> registeredServices = ConcurrentHashMap.newKeySet();
    private final Object registrationLock = new Object();

    /**
     * 动态调用方法（无参数）
     * @param methodExpression 方法表达式，如：quantifyCountService.sumWeightCountTotal()
     * @return 方法执行结果
     */
    public String invokeMethod(String methodExpression) {
        return invokeMethod(methodExpression, (Object[]) null);
    }

    /**
     * 动态调用方法（带参数）
     * @param methodExpression 方法表达式，如：cooperateEnterpriseService.todayTotalTrade(#args[0])
     * @param args 方法参数数组
     * @return 方法执行结果
     */
    public String invokeMethod(String methodExpression, Object... args) {
        if (methodExpression == null || methodExpression.trim().isEmpty()) {
            return "0";
        }

        try {
            String spelExpression = convertToSpelExpression(methodExpression, args != null && args.length > 0);
            log.debug("原始表达式: {}, 转换后的SpEL表达式: {}, 参数: {}",
                     methodExpression, spelExpression,
                     args != null ? java.util.Arrays.toString(args) : "null");

            Expression expression = expressionCache.computeIfAbsent(spelExpression, parser::parseExpression);

            StandardEvaluationContext context = new StandardEvaluationContext();
            registerRequiredService(context, methodExpression);

            // 注册参数到SpEL上下文
            if (args != null && args.length > 0) {
                context.setVariable("args", args);
            }

            Object result = expression.getValue(context);
            return result != null ? result.toString() : "0";

        } catch (Exception e) {
            // 如果方法调用失败，尝试智能重试
            return handleMethodCallFailure(methodExpression, args, e);
        }
    }

    /**
     * 处理方法调用失败的情况
     */
    private String handleMethodCallFailure(String methodExpression, Object[] args, Exception originalException) {
        String errorMessage = originalException.getMessage();

        // 1. 如果是方法找不到的错误，尝试智能重试
        if (errorMessage != null && errorMessage.contains("cannot be found")) {
            return tryIntelligentRetry(methodExpression, args, originalException);
        }

        // 2. 如果是属性或字段找不到的错误，也尝试重试
        if (errorMessage != null && errorMessage.contains("cannot be found on null")) {
            return tryIntelligentRetry(methodExpression, args, originalException);
        }

        // 3. 其他错误直接记录并返回默认值
        log.error("动态方法调用失败: methodExpression={}, args={}", methodExpression,
                 args != null ? java.util.Arrays.toString(args) : "null", originalException);
        return "0";
    }

    /**
     * 智能重试方法调用
     */
    private String tryIntelligentRetry(String methodExpression, Object[] args, Exception originalException) {
        try {
            // 1. 如果原来没有参数，尝试添加默认参数
            if (args == null || args.length == 0) {
                log.debug("尝试使用默认参数重新调用: {}", methodExpression);
                return invokeMethodDirectly(methodExpression, "", ""); // 提供两个默认参数
            }

            // 2. 如果原来有参数，尝试无参数调用
            if (args.length > 0) {
                log.debug("尝试无参数重新调用: {}", methodExpression);
                return invokeMethodDirectly(methodExpression); // 无参数调用
            }

        } catch (Exception retryException) {
            log.debug("智能重试也失败: {}", retryException.getMessage());
        }

        // 如果重试也失败，记录原始错误并返回默认值
        log.error("动态方法调用失败，智能重试也失败: methodExpression={}", methodExpression, originalException);
        return "0";
    }

    /**
     * 直接调用方法，不进行重试（避免递归）
     */
    private String invokeMethodDirectly(String methodExpression, Object... args) {
        try {
            String spelExpression = convertToSpelExpression(methodExpression, args != null && args.length > 0);
            log.debug("直接调用 - 原始表达式: {}, 转换后的SpEL表达式: {}, 参数: {}",
                     methodExpression, spelExpression,
                     args != null ? java.util.Arrays.toString(args) : "null");

            Expression expression = expressionCache.computeIfAbsent(spelExpression, parser::parseExpression);

            StandardEvaluationContext context = new StandardEvaluationContext();
            registerRequiredService(context, methodExpression);

            // 注册参数到SpEL上下文
            if (args != null && args.length > 0) {
                context.setVariable("args", args);
            }

            Object result = expression.getValue(context);
            return result != null ? result.toString() : "0";

        } catch (Exception e) {
            log.error("直接方法调用失败: methodExpression={}, args={}", methodExpression,
                     args != null ? java.util.Arrays.toString(args) : "null", e);
            return "0";
        }
    }

    /**
     * 转换为SpEL表达式格式
     * @param methodExpression 方法表达式
     * @param hasArgs 是否有参数
     */
    private String convertToSpelExpression(String methodExpression, boolean hasArgs) {
        // 如果已经是SpEL格式（以#开头），直接返回
        if (methodExpression.startsWith("#")) {
            return methodExpression;
        }

        // 查找第一个点的位置，在Bean名称前添加#
        int dotIndex = methodExpression.indexOf('.');
        if (dotIndex > 0) {
            String beanName = methodExpression.substring(0, dotIndex);
            String methodPart = methodExpression.substring(dotIndex);

            // 处理方法参数
            methodPart = processMethodParameters(methodPart, hasArgs);

            return "#" + beanName + methodPart;
        }

        // 如果没有点，可能是简单的Bean引用
        return "#" + methodExpression;
    }

    /**
     * 处理方法参数，将Java方法签名转换为SpEL表达式
     * @param methodPart 方法部分，如：.todayTotalFunctionDatas(phone, startDate)
     * @param hasArgs 是否有参数
     * @return 处理后的方法部分
     */
    private String processMethodParameters(String methodPart, boolean hasArgs) {
        // 如果方法中包含参数名（如 phone, startDate），需要替换为参数占位符
        if (methodPart.contains("(") && methodPart.contains(")")) {
            int startParen = methodPart.indexOf('(');
            int endParen = methodPart.lastIndexOf(')');

            if (startParen < endParen) {
                String methodName = methodPart.substring(0, startParen);
                String paramsPart = methodPart.substring(startParen + 1, endParen);

                // 如果参数部分不为空且不是已经处理过的#args格式
                if (!paramsPart.trim().isEmpty() && !paramsPart.contains("#args")) {
                    // 计算参数个数
                    String[] params = paramsPart.split(",");
                    int paramCount = params.length;

                    // 如果有参数，替换为#args[0], #args[1]等格式
                    if (hasArgs && paramCount > 0) {
                        StringBuilder newParams = new StringBuilder();
                        for (int i = 0; i < paramCount; i++) {
                            if (i > 0) newParams.append(", ");
                            newParams.append("#args[").append(i).append("]");
                        }
                        return methodName + "(" + newParams + ")";
                    } else {
                        // 如果没有传递参数但方法需要参数，返回空参数调用
                        return methodName + "()";
                    }
                } else if (paramsPart.trim().isEmpty() && hasArgs) {
                    // 如果是空括号但有参数，添加参数
                    return methodName + "(#args[0])";
                }
            }
        } else if (hasArgs && !methodPart.contains("(")) {
            // 如果没有括号但有参数，添加参数括号
            return methodPart + "(#args[0])";
        }

        return methodPart;
    }

    /**
     * 按需注册所需的Service Bean到SpEL上下文
     * @param context SpEL上下文
     * @param methodExpression 方法表达式
     */
    private void registerRequiredService(StandardEvaluationContext context, String methodExpression) {
        String serviceName = extractServiceName(methodExpression);

        if (serviceName != null) {
            // 每次都需要注册到当前上下文，因为每次调用都会创建新的上下文
            Object bean = findServiceBean(serviceName);
            if (bean != null) {
                String className = bean.getClass().getName();

                // 只注册项目包下的服务
                if (className.startsWith("com.mall.project.service")) {
                    context.setVariable(serviceName, bean);
                    registeredServices.add(serviceName); // 记录已发现的服务
                    log.debug("注册Service Bean: {} -> {}", serviceName, className);
                } else {
                    log.warn("服务 {} 不在项目包范围内，跳过注册: {}", serviceName, className);
                }
            } else {
                log.error("无法找到服务: {}", serviceName);
            }
        } else {
            log.error("无法从表达式中提取服务名称: {}", methodExpression);
        }
    }

    /**
     * 从方法表达式中提取服务名称
     * @param methodExpression 方法表达式，如：quantifyCountService.sumWeightCountTotal()
     * @return 服务名称，如：quantifyCountService
     */
    private String extractServiceName(String methodExpression) {
        if (methodExpression == null || methodExpression.trim().isEmpty()) {
            return null;
        }

        // 移除开头的#号（如果有）
        String expression = methodExpression.startsWith("#") ? methodExpression.substring(1) : methodExpression;

        // 查找第一个点的位置
        int dotIndex = expression.indexOf('.');
        if (dotIndex > 0) {
            return expression.substring(0, dotIndex);
        }

        return null;
    }



    /**
     * 智能查找Service Bean
     * @param serviceName 服务名称
     * @return Bean对象，如果找不到返回null
     */
    private Object findServiceBean(String serviceName) {
        try {
            // 1. 首先尝试直接通过Bean名称获取
            return applicationContext.getBean(serviceName);
        } catch (Exception e1) {
            // 2. 尝试添加Impl后缀
            String implName = serviceName + "Impl";
            try {
                Object bean = applicationContext.getBean(implName);
                log.debug("通过Impl后缀找到服务: {} -> {}", implName, bean.getClass().getSimpleName());
                return bean;
            } catch (Exception e2) {
                // 3. 尝试通过接口类型查找
                String interfaceName = convertToInterfaceName(serviceName);
                try {
                    if (interfaceName != null) {
                        Class<?> interfaceClass = Class.forName(interfaceName);
                        Object bean = applicationContext.getBean(interfaceClass);
                        log.debug("通过接口类型找到服务: {} -> {}", interfaceName, bean.getClass().getSimpleName());
                        return bean;
                    }
                } catch (Exception e3) {
                    // 4. 尝试智能模式匹配查找
                    Object bean = findServiceBeanBySmartPattern(serviceName);
                    if (bean != null) {
                        log.debug("通过智能匹配找到服务: {} -> {}", serviceName, bean.getClass().getSimpleName());
                        return bean;
                    }

                    // 5. 最后尝试在所有Service Bean中查找匹配的
                    bean = findServiceBeanByPattern(serviceName);
                    if (bean != null) {
                        log.debug("通过模糊匹配找到服务: {} -> {}", serviceName, bean.getClass().getSimpleName());
                        return bean;
                    }
                }
            }
        }

        log.warn("无法找到服务: {}", serviceName);
        return null;
    }

    /**
     * 将服务名称转换为接口类名
     * @param serviceName 服务名称，如：cooperateEnterpriseService
     * @return 接口类名，如：com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService
     */
    private String convertToInterfaceName(String serviceName) {
        if (serviceName == null || serviceName.isEmpty()) {
            return null;
        }

        // 移除Service后缀并转换为类名格式
        String baseName = serviceName.endsWith("Service") ?
            serviceName.substring(0, serviceName.length() - 7) : serviceName;

        // 首字母大写
        String className = Character.toUpperCase(baseName.charAt(0)) + baseName.substring(1);

        // 构建完整的接口类名
        return "com.mall.project.service." + baseName + "." + className + "Service";
    }

    /**
     * 通过智能模式匹配查找Service Bean
     * @param serviceName 服务名称
     * @return Bean对象，如果找不到返回null
     */
    private Object findServiceBeanBySmartPattern(String serviceName) {
        try {
            Map<String, Object> serviceBeans = applicationContext.getBeansWithAnnotation(Service.class);

            for (Map.Entry<String, Object> entry : serviceBeans.entrySet()) {
                String beanName = entry.getKey();
                Object bean = entry.getValue();
                String className = bean.getClass().getName();

                // 只考虑项目包下的服务
                if (className.startsWith("com.mall.project.service")) {
                    // 智能匹配逻辑
                    if (isServiceMatch(serviceName, beanName, className)) {
                        return bean;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("智能模式匹配查找失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 判断服务是否匹配
     */
    private boolean isServiceMatch(String serviceName, String beanName, String className) {
        // 1. 直接匹配Bean名称
        if (beanName.equals(serviceName)) {
            return true;
        }

        // 2. 匹配Bean名称 + Impl
        if (beanName.equals(serviceName + "Impl")) {
            return true;
        }

        // 3. 从类名推导服务名称进行匹配
        String derivedServiceName = deriveServiceNameFromClassName(className);
        if (serviceName.equals(derivedServiceName)) {
            return true;
        }

        // 4. 模糊匹配（包含关系）
        if (beanName.toLowerCase().contains(serviceName.toLowerCase()) ||
            serviceName.toLowerCase().contains(beanName.toLowerCase())) {
            return true;
        }

        return false;
    }

    /**
     * 从类名推导服务名称
     * 例如：com.mall.project.service.functionDatas.impl.FunctionDatasServiceImpl -> functionDatasService
     */
    private String deriveServiceNameFromClassName(String className) {
        try {
            // 提取类名
            String simpleClassName = className.substring(className.lastIndexOf('.') + 1);

            // 移除Impl后缀
            if (simpleClassName.endsWith("Impl")) {
                simpleClassName = simpleClassName.substring(0, simpleClassName.length() - 4);
            }

            // 首字母小写
            if (simpleClassName.length() > 0) {
                return Character.toLowerCase(simpleClassName.charAt(0)) + simpleClassName.substring(1);
            }
        } catch (Exception e) {
            log.debug("从类名推导服务名称失败: {}", className);
        }
        return null;
    }

    /**
     * 通过模式匹配查找Service Bean
     * @param serviceName 服务名称
     * @return Bean对象，如果找不到返回null
     */
    private Object findServiceBeanByPattern(String serviceName) {
        try {
            Map<String, Object> serviceBeans = applicationContext.getBeansWithAnnotation(Service.class);

            for (Map.Entry<String, Object> entry : serviceBeans.entrySet()) {
                String beanName = entry.getKey();
                Object bean = entry.getValue();
                String className = bean.getClass().getName();

                // 只考虑项目包下的服务
                if (className.startsWith("com.mall.project.service")) {
                    // 检查Bean名称是否匹配
                    if (beanName.equals(serviceName) ||
                        beanName.equals(serviceName + "Impl") ||
                        beanName.toLowerCase().contains(serviceName.toLowerCase())) {
                        return bean;
                    }
                }
            }
        } catch (Exception e) {
            // 静默处理，不输出日志
        }
        return null;
    }
}
