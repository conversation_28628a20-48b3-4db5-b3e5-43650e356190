package com.mall.project.controller.modulesSetManage;

import com.mall.common.api.CommonResult;
import com.mall.project.dto.modulesSetManage.ModulesParam;
import com.mall.project.service.modulesSetManage.ModulesSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功能模块控制器
 */
@RestController
@RequestMapping("/modules")
@Slf4j
public class ModulesSetController {
    
    @Autowired
    private ModulesSetService modulesSetService;
    
    /**
     * 获取所有模块列表
     */
    @GetMapping("/list")
    public CommonResult<List<ModulesParam>> list() {
        List<ModulesParam> modulesList = modulesSetService.listAll();
        return CommonResult.success(modulesList);
    }
    
    /**
     * 获取单个模块详情
     */
    @GetMapping("/{id}")
    public CommonResult<ModulesParam> getById(@PathVariable Integer id) {
        ModulesParam modules = modulesSetService.getById(id);
        if (modules != null) {
            return CommonResult.success(modules);
        }
        return CommonResult.failed("模块不存在");
    }
    
    /**
     * 新增模块
     */
    @PostMapping("/create")
    public CommonResult<String> create(@RequestBody ModulesParam modules) {
        boolean success = modulesSetService.create(modules);
        if (success) {
            return CommonResult.success("创建成功");
        }
        return CommonResult.failed("创建失败");
    }
    
    /**
     * 更新模块
     */
    @PutMapping("/update")
    public CommonResult<String> update(@RequestBody ModulesParam modules) {
        boolean success = modulesSetService.update(modules);
        if (success) {
            return CommonResult.success("更新成功");
        }
        return CommonResult.failed("更新失败");
    }
    
    /**
     * 删除模块
     */
    @DeleteMapping("/{id}")
    public CommonResult<String> delete(@PathVariable Integer id) {
        boolean success = modulesSetService.delete(id);
        if (success) {
            return CommonResult.success("删除成功");
        }
        return CommonResult.failed("删除失败");
    }
    
    /**
     * 获取子模块列表
     */
    @GetMapping("/parent/{parentId}")
    public CommonResult<List<ModulesParam>> listByParentId(@PathVariable Integer parentId) {
        List<ModulesParam> modulesList = modulesSetService.listByParentId(parentId);
        return CommonResult.success(modulesList);
    }

    /**
     * 获取模块路径
     */
    @GetMapping("/path/{moduleCode}")
    public CommonResult<String> getModulePath(@PathVariable String moduleCode) {
        String path = modulesSetService.getModulePath(moduleCode);
        return CommonResult.success(path);
    }
    
    /**
     * 获取下一个可用的模块代码
     * 根据级别和父模块ID生成
     * 如果提供了moduleId，则表示是编辑模式，会考虑是否需要重新生成代码
     */
    @GetMapping("/nextCode")
    public CommonResult<String> getNextModuleCode(@RequestParam("level") Integer level, 
                                                @RequestParam(value = "parentId", required = false, defaultValue = "0") Integer parentId,
                                                @RequestParam(value = "moduleId", required = false) Integer moduleId) {
        try {
            String nextCode = modulesSetService.generateModuleCode(level, parentId, moduleId);
            return CommonResult.success(nextCode);
        } catch (Exception e) {
            log.error("获取下一个模块代码失败", e);
            return CommonResult.failed("获取模块代码失败: " + e.getMessage());
        }
    }


    /**
     * 获取下一个可用的排序值
     * @param level 模块级别
     * @param parentId 父模块ID
     */
    @GetMapping("/nextSortOrder")
    public CommonResult<Integer> getNextSortOrder(@RequestParam("level") Integer level,
                                                  @RequestParam(value = "parentId", required = false, defaultValue = "0") Integer parentId) {
        try {
            int nextOrder = modulesSetService.generateNextSortOrder(level, parentId);
            return CommonResult.success(nextOrder);
        } catch (Exception e) {
            log.error("获取下一个排序值失败", e);
            return CommonResult.failed("获取排序值失败: " + e.getMessage());
        }
    }
}
