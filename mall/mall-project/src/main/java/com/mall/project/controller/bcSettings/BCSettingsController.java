package com.mall.project.controller.bcSettings;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.bcSettings.BCSettings;
import com.mall.project.service.bcSettings.BCSettingsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 描述 : 合作企业各ID B/C设置 控制器
 */

@RestController
public class BCSettingsController {

    @Autowired
    private BCSettingsService bcSettingsService;


    // 查询B设置信息
    @GetMapping("/bSettings")
    public CommonResult<Map<String, Object>> bSettings() {
        Map<String, Object> dataMap = bcSettingsService.getBSettings();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到B设置信息，请对合作企业各IDB进行设置");
        }
    }
    // 保存或更新B设置信息
    @PutMapping("/saveOrUpdateBSettings")
    public CommonResult<Map<String, Object>> saveOrUpdateBSettings(@RequestBody @Valid BCSettings pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = bcSettingsService.saveOrUpdateBSettings(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }
    // 查询C设置信息
    @GetMapping("/cSettings")
    public CommonResult<Map<String, Object>> cSettings() {
        Map<String, Object> dataMap = bcSettingsService.getCSettings();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到C设置信息，请对合作企业各IDC进行设置");
        }
    }
    // 保存或更新C设置信息
    @PutMapping("/saveOrUpdateCSettings")
    public CommonResult<Map<String, Object>> saveOrUpdateCSettings(@RequestBody @Valid BCSettings pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = bcSettingsService.saveOrUpdateCSettings(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }
}
