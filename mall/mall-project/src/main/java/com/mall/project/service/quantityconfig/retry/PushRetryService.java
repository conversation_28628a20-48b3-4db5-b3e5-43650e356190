package com.mall.project.service.quantityconfig.retry;

import com.mall.project.service.citationSettings.CitationSettingsService;
import com.mall.project.service.pushTomallB.PushTomallBService;
import com.mall.project.service.quantityconfig.QuantityConfigService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 推送重试服务
 * 处理推送失败后的重试逻辑，按照5分钟的倍数递增重试间隔
 */
@Service
@Slf4j
public class PushRetryService {

    @Autowired
    @Lazy
    private QuantityConfigService quantityConfigService;

    @Autowired
    @Lazy
    private PushTomallBService pushTomallBService;

    @Autowired
    @Lazy
    private CitationSettingsService citationSettingsService;
    
    private ThreadPoolTaskScheduler taskScheduler;
    
    // 存储重试任务信息：任务ID -> 重试次数
    private final Map<String, Integer> retryCountMap = new ConcurrentHashMap<>();

    // 存储任务开始时间：任务ID -> 开始时间
    private final Map<String, Instant> taskStartTimeMap = new ConcurrentHashMap<>();

    // 存储正在重试的推送类型：推送类型 -> 任务ID，防止同类型重复重试
    private final Map<Integer, String> activeRetryTasks = new ConcurrentHashMap<>();

    // 重试时间限制：23.5小时 = 1410分钟
    private static final long MAX_RETRY_DURATION_MINUTES = 23 * 60 + 30;

    // 任务ID计数器，确保唯一性
    private static final AtomicLong taskIdCounter = new AtomicLong(0);
    
    @PostConstruct
    public void init() {
        // 初始化线程池调度器
        taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(5);
        taskScheduler.setThreadNamePrefix("push-retry-");
        taskScheduler.initialize();

        // 启动定期清理任务，每小时执行一次
        taskScheduler.scheduleAtFixedRate(this::cleanupExpiredTasks, Duration.ofHours(1));
        log.info("推送重试服务初始化完成，定期清理任务已启动");
    }
    
    /**
     * 安排推送重试任务
     * @param taskId 任务ID，用于标识特定的推送任务
     * @param retryCount 当前重试次数
     * @param pushType 推送类型：1-分量配置，2-量化率，3-B/C授权，4-量化值，5-引流配置，6-平台补贴金
     */
    public void schedulePushRetry(String taskId, int retryCount, int pushType) {
        // 检查是否超过重试时间限制
        Instant startTime = taskStartTimeMap.get(taskId);
        if (startTime != null) {
            long elapsedMinutes = Duration.between(startTime, Instant.now()).toMinutes();
            if (elapsedMinutes >= MAX_RETRY_DURATION_MINUTES) {
                log.warn("推送重试任务 ID: {} 已超过时间限制 {} 分钟（23.5小时），停止重试", taskId, MAX_RETRY_DURATION_MINUTES);
                // 清理任务信息
                cleanupTask(taskId, pushType);
                return;
            }
        }

        // 计算下次重试间隔（分钟）：5 * (retryCount + 1)
        int delayMinutes = 5 * (retryCount + 1);

        // 检查下次重试时间是否会超过限制
        if (startTime != null) {
            long totalElapsedMinutes = Duration.between(startTime, Instant.now()).toMinutes() + delayMinutes;
            if (totalElapsedMinutes >= MAX_RETRY_DURATION_MINUTES) {
                log.warn("推送重试任务 ID: {} 下次重试将超过时间限制，停止重试", taskId);
                // 清理任务信息
                cleanupTask(taskId, pushType);
                return;
            }
        }

        log.info("安排推送重试任务 ID: {}, 类型: {}, 重试次数: {}, 延迟: {} 分钟", taskId, pushType, retryCount, delayMinutes);

        // 更新或保存重试次数
        retryCountMap.put(taskId, retryCount);

        // 安排延迟执行的任务
        taskScheduler.schedule(() -> {
            try {
                log.info("执行推送重试任务 ID: {}, 类型: {}, 重试次数: {}", taskId, pushType, retryCount);

                // 根据推送类型执行不同的推送方法
                switch (pushType) {
                    case 1:
                        quantityConfigService.pushTomallB();
                        break;
                    case 2:
                        pushTomallBService.pushQuantizationRate();
                        break;
                    case 3:
                        pushTomallBService.pushAreaAuthorize();
                        break;
                    case 4:
                        pushTomallBService.pushQuantizationValue();
                        break;
                    case 5:
                        citationSettingsService.pushTomallB();
                        break;
                    case 6:
                        pushTomallBService.pushPlatformGold();
                        break;
                    default:
                        log.warn("未知的推送类型: {}", pushType);
                        break;
                }

                // 推送成功，清理任务信息
                cleanupTask(taskId, pushType);
                log.info("推送重试成功 ID: {}, 类型: {}", taskId, pushType);
            } catch (Exception e) {
                log.error("推送重试失败 ID: {}, 类型: {}, 错误: {}", taskId, pushType, e.getMessage(), e);
                // 增加重试次数并再次安排重试
                schedulePushRetry(taskId, retryCount + 1, pushType);
            }
        }, Instant.now().plus(Duration.ofMinutes(delayMinutes)));
    }
    
    /**
     * 开始一个新的推送重试任务
     * @param pushType 推送类型：1-分量配置，2-量化率，3-B/C授权，4-量化值，5-引流配置，6-平台补贴金
     * @return 任务ID，如果该类型已有重试任务在进行，返回现有任务ID
     */
    public String startPushRetry(int pushType) {
        // 检查是否已有同类型的重试任务在进行
        String existingTaskId = activeRetryTasks.get(pushType);
        if (existingTaskId != null && retryCountMap.containsKey(existingTaskId)) {
            log.info("推送类型 {} 已有重试任务在进行，任务ID: {}，跳过新建重试任务", pushType, existingTaskId);
            return existingTaskId;
        }

        // 生成唯一的任务ID
        String taskId = "push-" + pushType + "-" + System.currentTimeMillis() + "-" + taskIdCounter.incrementAndGet();

        // 记录任务开始时间和活跃任务
        taskStartTimeMap.put(taskId, Instant.now());
        activeRetryTasks.put(pushType, taskId);

        log.info("开始新的推送重试任务，类型: {}，任务ID: {}", pushType, taskId);
        schedulePushRetry(taskId, 0, pushType);
        return taskId;
    }

    /**
     * 开始一个新的推送重试任务（默认为分量配置推送类型）
     * @return 任务ID
     */
    public String startPushRetry() {
        // 默认为分量配置推送(类型1)
        return startPushRetry(1);
    }
    
    /**
     * 检查任务是否存在
     * @param taskId 任务ID
     * @return 是否存在
     */
    public boolean hasTask(String taskId) {
        return retryCountMap.containsKey(taskId);
    }
    
    /**
     * 获取任务当前重试次数
     * @param taskId 任务ID
     * @return 重试次数，如果任务不存在则返回-1
     */
    public int getRetryCount(String taskId) {
        return retryCountMap.getOrDefault(taskId, -1);
    }

    /**
     * 获取任务已运行时间（分钟）
     * @param taskId 任务ID
     * @return 已运行时间（分钟），如果任务不存在则返回-1
     */
    public long getTaskElapsedMinutes(String taskId) {
        Instant startTime = taskStartTimeMap.get(taskId);
        if (startTime != null) {
            return Duration.between(startTime, Instant.now()).toMinutes();
        }
        return -1;
    }

    /**
     * 手动停止重试任务
     * @param taskId 任务ID
     * @return 是否成功停止
     */
    public boolean stopRetryTask(String taskId) {
        // 查找任务对应的推送类型
        Integer pushType = null;
        for (Map.Entry<Integer, String> entry : activeRetryTasks.entrySet()) {
            if (taskId.equals(entry.getValue())) {
                pushType = entry.getKey();
                break;
            }
        }

        boolean removed = retryCountMap.containsKey(taskId);
        if (removed && pushType != null) {
            cleanupTask(taskId, pushType);
            log.info("手动停止推送重试任务 ID: {}, 类型: {}", taskId, pushType);
        }
        return removed;
    }

    /**
     * 清理任务相关的所有信息
     * @param taskId 任务ID
     * @param pushType 推送类型
     */
    private void cleanupTask(String taskId, int pushType) {
        retryCountMap.remove(taskId);
        taskStartTimeMap.remove(taskId);
        // 只有当前活跃任务是这个taskId时才移除，避免误删新任务
        activeRetryTasks.remove(pushType, taskId);
    }

    /**
     * 获取指定推送类型的活跃重试任务ID
     * @param pushType 推送类型
     * @return 任务ID，如果没有活跃任务则返回null
     */
    public String getActiveRetryTask(int pushType) {
        return activeRetryTasks.get(pushType);
    }

    /**
     * 检查指定推送类型是否有重试任务在进行
     * @param pushType 推送类型
     * @return 是否有重试任务在进行
     */
    public boolean hasActiveRetryTask(int pushType) {
        String taskId = activeRetryTasks.get(pushType);
        return taskId != null && retryCountMap.containsKey(taskId);
    }

    /**
     * 获取当前所有活跃的重试任务数量
     * @return 活跃任务数量
     */
    public int getActiveTaskCount() {
        return (int) activeRetryTasks.values().stream()
                .filter(taskId -> retryCountMap.containsKey(taskId))
                .count();
    }

    /**
     * 清理所有过期或无效的任务信息（用于定期维护）
     */
    public void cleanupExpiredTasks() {
        Instant now = Instant.now();

        // 清理超时的任务
        retryCountMap.entrySet().removeIf(entry -> {
            String taskId = entry.getKey();
            Instant startTime = taskStartTimeMap.get(taskId);
            if (startTime != null) {
                long elapsedMinutes = Duration.between(startTime, now).toMinutes();
                if (elapsedMinutes >= MAX_RETRY_DURATION_MINUTES) {
                    log.warn("清理超时任务 ID: {}", taskId);
                    taskStartTimeMap.remove(taskId);
                    // 从活跃任务中移除
                    activeRetryTasks.entrySet().removeIf(activeEntry ->
                        taskId.equals(activeEntry.getValue()));
                    return true;
                }
            }
            return false;
        });

        // 清理孤立的开始时间记录
        taskStartTimeMap.entrySet().removeIf(entry ->
            !retryCountMap.containsKey(entry.getKey()));

        // 清理孤立的活跃任务记录
        activeRetryTasks.entrySet().removeIf(entry ->
            !retryCountMap.containsKey(entry.getValue()));

        log.debug("任务清理完成，当前活跃任务数: {}", getActiveTaskCount());
    }
} 