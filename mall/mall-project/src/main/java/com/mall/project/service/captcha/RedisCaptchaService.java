package com.mall.project.service.captcha;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import java.util.concurrent.TimeUnit;

@Service
public class RedisCaptchaService {

    private static final String CAPTCHA_PREFIX = "captcha:";
    private static final long CAPTCHA_EXPIRE_MINUTES = 5;

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void saveCaptcha(String key, String value) {
        redisTemplate.opsForValue().set(
                CAPTCHA_PREFIX + key,
                value,
                CAPTCHA_EXPIRE_MINUTES,
                TimeUnit.MINUTES
        );
    }

    public String getCaptcha(String key) {
        return redisTemplate.opsForValue().get(CAPTCHA_PREFIX + key);
    }

    public void deleteCaptcha(String key) {
        redisTemplate.delete(CAPTCHA_PREFIX + key);
    }

    public boolean validateCaptcha(String key, String input) {
        String captcha = getCaptcha(key);
        if (captcha == null) {
            return false;
        }

        if (captcha.equals(input)) {
            deleteCaptcha(key);
            return true;
        }
        return false;
    }
}
