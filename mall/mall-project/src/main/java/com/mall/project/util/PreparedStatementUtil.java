package com.mall.project.util;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;

import static com.mall.common.util.StringUtils.isEmpty;

/**
 * PreparedStatement工具类
 * 用于自动设置PreparedStatement的参数
 */
public class PreparedStatementUtil {

    /**
     * 使用反射自动设置PreparedStatement的参数
     *
     * @param ps PreparedStatement对象
     * @param pojo 实体类对象
     * @param additionalParams 额外需要设置的参数（如updatePerson等）
     * @throws Exception 设置参数时可能抛出的异常
     */
    public static void setPreparedStatementParams(PreparedStatement ps, Object pojo, Object... additionalParams) throws Exception {
        Field[] fields = pojo.getClass().getDeclaredFields();
        int paramIndex = 1;
        
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(pojo);
            
            if (value == null) {
                ps.setNull(paramIndex++, getSqlType(field.getType()));
                continue;
            }
            
            // 根据字段类型设置参数
            if (value instanceof String) {
                ps.setString(paramIndex++, isEmpty((String) value) ? null : (String) value);
            } else if (value instanceof Integer) {
                ps.setInt(paramIndex++, (Integer) value);
            } else if (value instanceof Long) {
                ps.setLong(paramIndex++, (Long) value);
            } else if (value instanceof Double) {
                ps.setDouble(paramIndex++, (Double) value);
            } else if (value instanceof Float) {
                ps.setFloat(paramIndex++, (Float) value);
            } else if (value instanceof Boolean) {
                ps.setBoolean(paramIndex++, (Boolean) value);
            } else if (value instanceof java.util.Date) {
                ps.setTimestamp(paramIndex++, new java.sql.Timestamp(((java.util.Date) value).getTime()));
            } else if (value instanceof java.time.LocalDateTime) {
                ps.setTimestamp(paramIndex++, java.sql.Timestamp.valueOf((java.time.LocalDateTime) value));
            } else if (value instanceof java.time.LocalDate) {
                ps.setDate(paramIndex++, java.sql.Date.valueOf((java.time.LocalDate) value));
            } else {
                // 对于其他类型，转换为字符串处理
                ps.setString(paramIndex++, value.toString());
            }
        }

        // 设置额外参数
        if (additionalParams != null) {
            for (Object param : additionalParams) {
                if (param == null) {
                    ps.setNull(paramIndex++, java.sql.Types.VARCHAR);
                    continue;
                }
                
                if (param instanceof String) {
                    ps.setString(paramIndex++, (String) param);
                } else if (param instanceof Integer) {
                    ps.setInt(paramIndex++, (Integer) param);
                } else if (param instanceof Long) {
                    ps.setLong(paramIndex++, (Long) param);
                } else if (param instanceof Double) {
                    ps.setDouble(paramIndex++, (Double) param);
                } else if (param instanceof Float) {
                    ps.setFloat(paramIndex++, (Float) param);
                } else if (param instanceof Boolean) {
                    ps.setBoolean(paramIndex++, (Boolean) param);
                } else if (param instanceof java.util.Date) {
                    ps.setTimestamp(paramIndex++, new java.sql.Timestamp(((java.util.Date) param).getTime()));
                } else if (param instanceof java.time.LocalDateTime) {
                    ps.setTimestamp(paramIndex++, java.sql.Timestamp.valueOf((java.time.LocalDateTime) param));
                } else if (param instanceof java.time.LocalDate) {
                    ps.setDate(paramIndex++, java.sql.Date.valueOf((java.time.LocalDate) param));
                } else {
                    ps.setString(paramIndex++, param.toString());
                }
            }
        }
    }

    /**
     * 获取字段对应的SQL类型
     */
    private static int getSqlType(Class<?> type) {
        if (type == String.class) {
            return java.sql.Types.VARCHAR;
        } else if (type == Integer.class || type == int.class) {
            return java.sql.Types.INTEGER;
        } else if (type == Long.class || type == long.class) {
            return java.sql.Types.BIGINT;
        } else if (type == Double.class || type == double.class) {
            return java.sql.Types.DOUBLE;
        } else if (type == Float.class || type == float.class) {
            return java.sql.Types.FLOAT;
        } else if (type == Boolean.class || type == boolean.class) {
            return java.sql.Types.BOOLEAN;
        } else if (type == java.util.Date.class || type == java.time.LocalDateTime.class) {
            return java.sql.Types.TIMESTAMP;
        } else if (type == java.time.LocalDate.class) {
            return java.sql.Types.DATE;
        }
        return java.sql.Types.VARCHAR;
    }
} 