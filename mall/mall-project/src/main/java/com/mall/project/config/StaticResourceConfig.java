package com.mall.project.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态资源配置
 * 确保静态资源路径不与API路径冲突
 */
@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射 - 使用明确的路径避免与API冲突
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600); // 缓存1小时

        // 模块管理页面资源
        registry.addResourceHandler("/modulesSet/**")
                .addResourceLocations("classpath:/static/modulesSet/")
                .setCachePeriod(3600);

        // 二维码资源
        registry.addResourceHandler("/qrcode/**")
                .addResourceLocations("classpath:/static/qrcode/")
                .setCachePeriod(3600);

        // 为根路径下的HTML文件提供访问，但排除API路径
        registry.addResourceHandler("/*.html", "/*.css", "/*.js", "/*.png", "/*.jpg", "/*.gif", "/*.ico")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600);
    }
}
