package com.mall.project.service.quantityconfig.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.project.dao.quantityconfig.QuantityConfigDao;
import com.mall.project.dto.quantityconfig.QuantityConfigParam;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.quantityconfig.QuantityConfigService;
import com.mall.project.service.quantityconfig.retry.PushRetryService;
import com.mall.project.util.MallBAuthUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 分量配置服务实现类
 * <p>
 * 实现了 QuantityConfigService 接口，提供了分量配置的具体业务逻辑。
 * </p>
 */
@Service
public class QuantityConfigServiceImpl implements QuantityConfigService {

    @Autowired
    private QuantityConfigDao quantityConfigDao;

    @Autowired
    private PushRetryService pushRetryService;
    
    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 获取当前分量配置
     */
    @Override
    public QuantityConfigParam getQuantityConfig() {
        return quantityConfigDao.selectOne();
    }

    /**
     * @param param 待保存或更新的分量配置数据。
     */
    @Override
    @Transactional
    public QuantityConfigParam saveOrUpdateQuantityConfig(QuantityConfigParam param) {
        QuantityConfigParam existingConfig = quantityConfigDao.selectOne();

        if (existingConfig != null) {
            // 配置已存在，执行更新操作
            // 确保使用现有记录的ID进行更新
            param.setId(existingConfig.getId());
            quantityConfigDao.update(param);

            // 更新后重新从数据库获取，以确保数据一致性（例如获取数据库自动更新的时间戳）
            QuantityConfigParam updatedConfig = quantityConfigDao.selectOne();
            if (updatedConfig == null) {
                // log.error("更新分量配置后重新获取失败，ID: {}", quantityConfig.getId()); // 如需日志，取消注释并引入Logger
                throw new BusinessException("更新分量配置后重新获取失败，ID为: " + param.getId());
            }
            return updatedConfig;
        } else {
            // 配置不存在，执行插入操作
            // DAO中的insert方法应在插入成功后将自增ID设置回quantityConfig对象
            quantityConfigDao.insert(param);

            if (param.getId() != null) {
                QuantityConfigParam newConfig = quantityConfigDao.selectOne();
                if (newConfig == null) {
                    throw new BusinessException("插入分量配置后重新获取失败，生成的ID为: " + param.getId());
                }
                return newConfig;
            } else {
                throw new BusinessException("分量配置插入操作成功，但ID未能回填到DTO中。");
            }
        }
    }

    @Override
    public void pushTomallB() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            
            // 构建符合mallB要求的请求体格式
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("type", 1);  //数据类似 1：分量配置，2：引流配置

            QuantityConfigParam param =  quantityConfigDao.selectOne();
            
            // 创建jsonObject内部数据
            Map<String, String> jsonObject = new HashMap<>();
            jsonObject.put("initialThreshold", param.getInitialThreshold() != null ? param.getInitialThreshold() : "");
            jsonObject.put("dailyThreshold", param.getDailyThreshold() != null ? param.getDailyThreshold() : "");
            jsonObject.put("dailyThresholdReward", param.getDailyThresholdReward() != null ? param.getDailyThresholdReward() : "");
            jsonObject.put("recommendMerchantsNum", param.getRecommendMerchantsNum() != null ? param.getRecommendMerchantsNum() : "");
            jsonObject.put("recommendMerchantsReward", param.getRecommendMerchantsReward() != null ? param.getRecommendMerchantsReward() : "");
            jsonObject.put("monthlyTechThreshold", param.getMonthlyTechThreshold() != null ? param.getMonthlyTechThreshold() : "");
            jsonObject.put("monthlyTechRewardGold", param.getMonthlyTechRewardGold() != null ? param.getMonthlyTechRewardGold() : "");
            jsonObject.put("monthlyNoTechThreshold", param.getMonthlyNoTechThreshold() != null ? param.getMonthlyNoTechThreshold() : "");
            jsonObject.put("monthlyNoTechPenaltyGold", param.getMonthlyNoTechPenaltyGold() != null ? param.getMonthlyNoTechPenaltyGold() : "");
            jsonObject.put("dailyExtraQuantityUnit", param.getDailyExtraQuantityUnit() != null ? param.getDailyExtraQuantityUnit() : "");
            jsonObject.put("dailyExtraRewardGold", param.getDailyExtraRewardGold() != null ? param.getDailyExtraRewardGold() : "");
            jsonObject.put("onOff", param.getOnOff() != null ? param.getOnOff() : "");
            
            requestMap.put("jsonObject", jsonObject);
            
            //System.out.println("推送到mallB的数据: " + objectMapper.writeValueAsString(requestMap));
            
            // 直接使用工具类发送POST请求
            ResponseEntity<String> configResponse = mallBAuthUtils.postForEntity("/mall/receptionA/receiveData", requestMap, String.class);
            
            //System.out.println("推送响应状态: " + configResponse.getStatusCode());
            //System.out.println("推送响应内容: " + configResponse.getBody());
            
            if (configResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("推送分量配置到mallB系统失败: " + configResponse.getStatusCode());
            }
            
            // 解析响应
            if (configResponse.getBody() != null) {
                JsonNode responseNode = objectMapper.readTree(configResponse.getBody());
                int code = responseNode.path("code").asInt();
                String msg = responseNode.path("msg").asText();
                
                if (code != 200) {
                    throw new BusinessException("推送分量配置到mallB系统失败: " + msg);
                }
                
                System.out.println("推送分量配置到mallB系统成功: " + msg);
            }
            
        } catch (Exception e) {
            System.out.println("推送分量配置到mallB系统失败: " + e.getMessage());
            // 安排重试任务 暂时不启用该功能
            pushRetryService.startPushRetry(1);
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            } else {
                throw new BusinessException("与mallB系统通信失败: " + e.getMessage());
            }
        }
    }
}
