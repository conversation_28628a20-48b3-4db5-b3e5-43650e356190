package com.mall.project.service.dailyTradePercentage;

import com.mall.project.dto.dailyTradePercentage.DailyTradePercentage;

import java.util.Map;

/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置
 */
public interface DailyTradePercentageService {

    /**
     * 获取所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public Map<String, Object> getDailyTradePercentage();

    /**
     * 添加所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public Map<String, Object> saveOrUpdateDailyTradePercentage(DailyTradePercentage pojo, Integer updatePerson);
}
