package com.mall.project.dto.dailyTradePercentage;

import lombok.Data;

/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置表
 */
@Data
public class DailyTradePercentage {

    //所有企业各ID每日每笔新交易数据的量化数比配置表开、关
    private String isEnabled;

    //所有企业各ID每日每笔新交易数据的量化数比 (%)
    private String dailyTradePercentage;
    //排位1
    private String ranking1;

    //排位1设置获取百分比 (%)
    private String ranking1Percentage;

    // 排位2
    private String ranking2;

    // 排位2设置获取百分比 (%)
    private String ranking2Percentage;
}
