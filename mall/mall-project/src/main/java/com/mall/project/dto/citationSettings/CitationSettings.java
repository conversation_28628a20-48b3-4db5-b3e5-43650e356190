package com.mall.project.dto.citationSettings;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 描述: 引流设置
 */
@Data
public class CitationSettings {

    // 替换正则验证为数值范围验证
    @Min(value = 1, message = "引流设置id必须为正整数")
    private Integer id;                      // 引流设置id
    // 验证只能是0或1
    @Pattern(regexp = "^[01]$", message = "引流设置开关只能为0或1")
    @NotEmpty(message = "引流设置开关不能为空")
    private String isEnabled;                //开启/关闭引流设置,1-开; 0-关

    private String citationValue;             //单次引流值
}
