package com.mall.project.controller.captcha;
import com.mall.project.service.captcha.RedisCaptchaService;
import com.mall.project.util.CaptchaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
public class CaptchaController {

    @Autowired
    private RedisCaptchaService redisCaptchaService;

    @GetMapping(value = "/captcha")
    public ResponseEntity<Map> getCaptcha() throws IOException {
        // 生成验证码
        CaptchaUtils.MathCaptcha captcha = CaptchaUtils.generateMathCaptcha();
        // 生成唯一token作为key
        String token = UUID.randomUUID().toString();

        // 存储验证码到Redis，5分钟过期
        redisCaptchaService.saveCaptcha(token, captcha.getResult());

        String encode = cn.hutool.core.codec.Base64.encode(captcha.getImageBytes());
        Map hashMap = new HashMap();
        hashMap.put("uuid",token);
        hashMap.put("img",encode);
        return ResponseEntity.ok(hashMap);
    }

    /**
     * 验证验证码
     * @param token
     * @param input
     * @return
     */
    @GetMapping("/verify")
    public Boolean verifyCaptcha(String token, String input) {
        boolean isValid = redisCaptchaService.validateCaptcha(token, input);
        if (isValid) {
            return true;
        } else {
            return false;
        }
    }
}