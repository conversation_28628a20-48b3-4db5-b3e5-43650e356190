package com.mall.project.util;

import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


@Component
public class MD5Util {
    public String encryptMd5(String phone, String password) {
        try {
            // 拼接三个参数
            String rawData = phone + password;

            // 创建MD5加密实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(rawData.getBytes());

            // 获取加密字节数组
            byte[] digest = md.digest();

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }
}
