package com.mall.project.service.quantizationValue.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.quantizationValue.QuantizationValueDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化值服务实现类
 */
@Service
@Slf4j
public class QuantizationValueServiceImpl implements QuantizationValueService {

    @Autowired
    private QuantizationValueDao quantizationValueDao;

    @Autowired
    private QuantifyCountService quantifyCountService;

    /**
     * 量化值计算
     */
    @Override
    public void updateQuantizationValue() {
        quantizationValueDao.updateQuantizationValue();
    }

    /**
     * 计算 每日Admin量化值 =  Admin的累计量化数 乘以 当日量化率
     */
    @Override
    public void adminDailyQuantifyValue(String startTime) {
        // 获取当日量化率
        BigDecimal latestQuantizationRate = new BigDecimal(getLatestQuantizationRate(startTime));
        String adminDailyQuantity = quantifyCountService.adminTotalQuantity(startTime);     //Admin的累计量化数
        String value = new BigDecimal(adminDailyQuantity).multiply(latestQuantizationRate).setScale(2, RoundingMode.DOWN).toString();
        quantizationValueDao.saveAdminQuantifyValue(value);   //保存 每日Admin量化值 到数据库
    }

    /**
     * 每日Admin量化值
     */
    @Override
    public String queryAdminDailyQuantifyValue(String startTime) {
        return quantizationValueDao.queryAdminQuantizationValue(startTime);
    }

    /**
     * 计算 每日累计Admin量化值
     */
    @Override
    public String adminDailyQuantizationValue(String startTime) {
        return quantizationValueDao.totalAdminAdminQuantifyValue(startTime);
    }

    /**
     * 查询量化值, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryQuantizationValuePages(String phone, String startDate, String endDate, int pageNum, int pageSize , Integer isGreaterThanZero) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = quantizationValueDao.queryQuantizationValuePages(phone, startDate, endDate, pageSize, offset, isGreaterThanZero);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = quantizationValueDao.totalQuantizationValue(phone, startDate, endDate , isGreaterThanZero);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        QuantizationValueServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new QuantizationValueServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        //每日Admin量化值
        summary.put("adminDailyQuantifyValue", ConvertToCamelCase.formatDecimal(new BigDecimal(queryAdminDailyQuantifyValue(startDate))));
        //每日累计Admin量化值
        summary.put("adminDailyQuantizationValue", ConvertToCamelCase.formatDecimal(new BigDecimal(adminDailyQuantizationValue(startDate))));
        //每日量化值累计
        summary.put("everydayTotalQuantizationValue", ConvertToCamelCase.formatDecimal(new BigDecimal(everydayTotalQuantizationValue(phone, startDate))));
        //总量化值累计
        summary.put("allTotalQuantizationValue", ConvertToCamelCase.formatDecimal(new BigDecimal(allTotalQuantizationValue(phone, startDate))));
        //每日量化累计
        summary.put("everydayTotalCreditValue", ConvertToCamelCase.formatDecimal(new BigDecimal(everydayTotalCreditValue(phone, startDate))));
        //总量化累计
        summary.put("allTotalCreditValue", ConvertToCamelCase.formatDecimal(new BigDecimal(allTotalCreditValue(phone, startDate))));
        //每日平台补贴金
        summary.put("todayTotalPlatformGold", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalPlatformGold(phone, startDate))));
        //累计平台补贴金
        summary.put("totalPlatformGold", ConvertToCamelCase.formatDecimal(new BigDecimal(totalPlatformGold(phone, startDate))));
        //每日平台促销金
        summary.put("todayTotalPromotionGold", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalPromotionGold(phone, startDate))));
        //累计平台促销金
        summary.put("totalPromotionGold", ConvertToCamelCase.formatDecimal(new BigDecimal(totalPromotionGold(phone, startDate))));
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 导出量化值 Excel
     */
    @Override
    public List<Map<String, Object>> exportQuantizationValueExcel(String phone, String startDate, String endDate) {
        List<Map<String, Object>> dataList = quantizationValueDao.exportQuantizationValueExcel(phone,startDate, endDate);
        if(dataList.isEmpty()){
            throw new RuntimeException("暂无数据");
        }
        return dataList.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 每日平台补贴金
     */
    @Override
    public String todayTotalPlatformGold(String phone,String startTime) {
        String result = quantizationValueDao.todayTotalPlatformGold(phone,startTime);
        return result == null ? "0" : result;
    }

    /**
     * 累计平台补贴金
     */
    @Override
    public String totalPlatformGold(String phone,String startTime) {
        String result = quantizationValueDao.totalPlatformGold(phone,startTime);
        return result == null ? "0" : result;
    }

    /**
     * 每日平台促销金
     */
    @Override
    public String todayTotalPromotionGold(String phone,String startTime) {
        String result = quantizationValueDao.todayTotalPromotionGold(phone,startTime);
        return result == null ? "0" : result;
    }


    /**
     * 累计平台促销金
     */
    @Override
    public String totalPromotionGold(String phone,String startTime) {
        String result = quantizationValueDao.totalPromotionGold(phone,startTime);
        return result == null ? "0" : result;
    }
    /**
     * 每日量化值累计
     */
    @Override
    public String everydayTotalQuantizationValue(String phone,String startTime) {
        return quantizationValueDao.everydayTotalQuantizationValue(phone,startTime);
    }
    /**
     * 总量化值累计
     */
    @Override
    public String allTotalQuantizationValue(String phone,String startTime) {
        return quantizationValueDao.allTotalQuantizationValue(phone,startTime);
    }
    /**
     * 每日量化累计
     */
    @Override
    public String everydayTotalCreditValue(String phone,String startTime) {
        return quantizationValueDao.everydayTotalCreditValue(phone,startTime);
    }
    /**
     * 总量化累计
     */
    @Override
    public String allTotalCreditValue(String phone,String startTime) {
        return quantizationValueDao.allTotalCreditValue(phone,startTime);
    }

    /**
     * 查询B,CB的每日量化值,并推送到mallB系统
     */
    @Override
    public List<Map<String, Object>> queryTodayQuantizationValue() {
        return quantizationValueDao.queryTodayQuantizationValue();
    }

    /**
     * 查询Admin的每日量化值,并推送到mallB系统
     */
    @Override
    public String queryTodayAdminQuantizationValue() {
        return quantizationValueDao.queryTodayAdminQuantizationValue();
    }

    /**
     * 查询每日平台补贴金,并推送到mallB系统
     */
    @Override
    public List<Map<String, Object>> queryTodayPlatformGold() {
        return quantizationValueDao.queryTodayPlatformGold();
    }

    /**
     * 查询最新日期的量化率
     */
    @Override
    public String getLatestQuantizationRate(String startTime) {
        return quantizationValueDao.getLatestQuantizationRate(startTime);
    }
}
