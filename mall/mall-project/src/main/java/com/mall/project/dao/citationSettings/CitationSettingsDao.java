package com.mall.project.dao.citationSettings;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.Map;

import static com.mall.common.util.StringUtils.isEmpty;

@Repository
public class CitationSettingsDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 引流设置 新增 ,citation_settings 表有数据就修改,无数据就新增 使用 ON DUPLICATE KEY UPDATE
    public int addCitationSettings(String isEnabled, String citationValue, int updatePerson) {
        String sql = "INSERT INTO citation_settings(id, is_enabled, citation_value, update_person, update_time) VALUES (1, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "is_enabled = VALUES(is_enabled), citation_value = VALUES(citation_value), update_person = VALUES(update_person), update_time = NOW()";
        return jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql);
            // 处理空字符串，将其转换为null
            ps.setString(1, isEmpty(isEnabled) ? null : isEnabled);
            ps.setString(2, isEmpty(citationValue) ? "" : citationValue);
            ps.setInt(3, updatePerson);
            return ps;
        });
    }

    // 引流设置 查询
    public Map<String, Object> getCitationSettings() {
        String sql = "SELECT id, is_enabled, citation_value FROM citation_settings";
        return jdbcTemplate.queryForMap(sql);
    }
}
