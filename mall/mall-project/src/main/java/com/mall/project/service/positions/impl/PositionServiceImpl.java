package com.mall.project.service.positions.impl;


import com.mall.common.api.CommonPage;
import com.mall.project.dao.positions.PositionsDao;
import com.mall.project.service.positions.PositionsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class PositionServiceImpl implements PositionsService {

    @Autowired
    private PositionsDao positionsDao;
    
    @Override
    public Map<String, Object> getPositions() {
        List<Map<String, Object>> positions = positionsDao.getPositions();
        Map<String, Object> result = new HashMap<>();
        result.put("positions", positions);
        result.put("total", positions.size());
        return result;
    }

    @Override
    public List<Map<String, Object>> getPositionsForExport() {
        return positionsDao.getAllPositionsForExport();
    }

    /**分页查询职位信息
     * @param searchName The name to search for (currently unused).
     * @param pageNum    The page number to retrieve.
     * @param pageSize   The number of items per page.
     * @return
     */
    @Override
    public CommonPage<Map<String, Object>> getPositionsPaged(String searchName, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10; // Default page size
        }

        int offset = (pageNum - 1) * pageSize;

        List<Map<String, Object>> list = positionsDao.getPositionsPaged(searchName, pageSize, offset);
        long total = positionsDao.countPositions(searchName); // DAO returns int, CommonPage expects long for total

        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);

        return new CommonPage<>(pageNum, pageSize, totalPages, total, list);
    }
}
