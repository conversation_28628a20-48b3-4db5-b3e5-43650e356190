package com.mall.project.dto.mallBUsers;

import lombok.Data;

/**
 * mallB系统用户数据
 */
@Data
public class MallBUsers {
    private String id;                        //用户ID
    private String userName;                  //用户名
    private String parentId;                  //父级ID
    private String parentUserName;            //父级用户名
    private String userType;                  //用户类型(C消费者,CB代销,B商家)
    private String chainType;                 //链类型(C,B)
    private String status;                    //用户状态（0正常 1禁用 2 删除 3 失效 4 睡眠 5 无效）
    private String deductionMoneyLimit;       //平台兑换金上限
    private String businessName;              //企业名称
    private String phone;                     //手机号
    private int fans;                         //粉丝数
    private String address;                   //公司注册地址
    private String townCode;                  //详细地址编号
    private String jurisdiction;              //权限：权限1：1，权限2：2，权限3：3
    private String flag;                      //达标标志：1：是达标1；2：是达标2；0 未达标
    private String loginAddress;              //最近登录地址
    private String socialCreditCode;          //企业社会信用代码
    //private String quantifyCount;           //量化数(只有B用户有)：使用合计数 乘以 daily_trade_percentage表的 daily_trade_percentage 和 排位 计算 所得量化数
    private String todayWeight;               //今日分量（只有C用户有，CB，B用户没有，只是今天的分量）
    private String weightCount;               //累计分量(今日加历史以来的分量)（只有C用户有）：C 用户没有量化数，只有分量，但可以通过 使用 每日每ID的分量进化量化数 乘以 累计分量 得到 累计量化数
    private String createTime;                //创建时间


    private Integer pageNum;
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
