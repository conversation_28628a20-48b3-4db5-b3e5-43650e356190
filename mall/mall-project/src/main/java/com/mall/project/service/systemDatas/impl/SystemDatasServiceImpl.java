package com.mall.project.service.systemDatas.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.systemDatas.SystemDatasDao;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import com.mall.project.service.systemDatas.SystemDatasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 系统数据服务实现类
 */
@Service
@Slf4j
public class SystemDatasServiceImpl implements SystemDatasService {

    @Autowired
    private SystemDatasDao systemDatasDao;

    @Autowired
    private QuantizationValueService quantizationValueService;

    @Autowired
    private QuantifyCountService quantifyCountService;

    /**
     * 查询系统数据
     */
    @Override
    public Map<String, Object> querySystemDatas() {
        return ConvertToCamelCase.convertToCamelCase(systemDatasDao.querySystemDatas());
    }
    /**
     * 更新系统数据,这个由定时器执行,一般会在凌晨1点后执行
     */
    @Override
    public void updateSystemDatas(String startTime) {
        systemDatasDao.updateSystemDatas(sysQuantityValue(),sysCredit(),sysPromotionGold(),sysQuantityNum(startTime),sysSubsidyFunds(),sysVerifiedSubsidy(),sysWeightTotal());
    }

    /**
     * 系统量化值累计
     */
    public String sysQuantityValue(){
        return quantizationValueService.allTotalQuantizationValue("","");
    }

    /**
     * 系统信用值累计
     */
    public String sysCredit(){
        return "0";
    }

    /**
     * 系统促销金累计
     */
    public String sysPromotionGold(){
        return "0";
    }

    /**
     * 系统量化数累计 = Admin的累计量化数 + 今日累计量化数
     */
    public String sysQuantityNum(String startTime){
        BigDecimal adminTotalQuantity = new BigDecimal(quantifyCountService.adminTotalQuantity(startTime));  //Admin的累计量化数
        BigDecimal weightCountTotal = new BigDecimal(quantifyCountService.weightCountTotal("",startTime));  //今日累计量化数
        return adminTotalQuantity.add(weightCountTotal).toString();
    }

    /**
     * 系统补贴金累计
     */
    public String sysSubsidyFunds(){
        return "0";
    }

    /**
     * 系统已核销补贴金累计
     */
    public String sysVerifiedSubsidy(){
        return "0";
    }

    /**
     * 系统分量累计
     */
    public String sysWeightTotal(){
        return systemDatasDao.sysWeightTotal();
    }
}
