package com.mall.project.dao.quantityconfig;

import com.mall.project.dto.quantityconfig.QuantityConfigParam;
import com.mall.project.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;

import static com.mall.common.util.StringUtils.isEmpty;

/**
 * 分量配置数据访问对象
 * <p>
 * 负责直接与数据库交互，执行分量配置相关的增删改查操作。
 * </p>
 */
@Repository
public class QuantityConfigDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String TABLE_NAME = "quantity_config"; // 表名

    /**
     * 查询唯一的分量配置记录。
     * <p>
     * 系统中通常只有一条分量配置记录。此方法获取该记录。
     * </p>
     * @return QuantityConfig 对象，如果未找到记录则返回 null。
     */
    public QuantityConfigParam selectOne() {
        String sql = "SELECT id, initial_threshold AS initialThreshold, daily_threshold AS dailyThreshold, " +
                "daily_threshold_reward AS dailyThresholdReward, recommend_merchants_num AS recommendMerchantsNum, " +
                "recommend_merchants_reward AS recommendMerchantsReward, monthly_tech_threshold AS monthlyTechThreshold, " +
                "monthly_tech_reward_gold AS monthlyTechRewardGold, monthly_no_tech_threshold AS monthlyNoTechThreshold, " +
                "monthly_no_tech_penalty_gold AS monthlyNoTechPenaltyGold, daily_extra_quantity_unit AS dailyExtraQuantityUnit, " +
                "daily_extra_reward_gold AS dailyExtraRewardGold,on_off as OnOff, updated_time AS updatedTime " +
                "FROM " + TABLE_NAME + " ORDER BY id ASC LIMIT 1";
        try {
            List<QuantityConfigParam> configs = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(QuantityConfigParam.class));
            if (configs.isEmpty()) {
                return null;
            }
            return configs.get(0); // 返回第一条（也是唯一一条）记录
        } catch (org.springframework.dao.DataAccessException e) {
            throw new BusinessException("查询分量配置失败: " + e.getMessage());
        }
    }

    /**
     * 插入新的分量配置记录。
     * <p>
     * 此方法会将传入的 QuantityConfig 对象的数据保存到数据库中作为新记录。
     * 成功插入后，会尝试获取数据库生成的自增ID并设置回传入的 qc 对象的 id 字段。
     * </p>
     * @param qc 待插入的分量配置对象，其 id 字段会在插入成功后被更新。
     * @return 受影响的数据库行数，通常为 1 表示成功。
     */
    public int insert(QuantityConfigParam qc) {
        String sql = "INSERT INTO " + TABLE_NAME + " (initial_threshold, daily_threshold, daily_threshold_reward, " +
                "recommend_merchants_num, recommend_merchants_reward, monthly_tech_threshold, monthly_tech_reward_gold, " +
                "monthly_no_tech_threshold, monthly_no_tech_penalty_gold, daily_extra_quantity_unit, daily_extra_reward_gold,on_off) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();

        int affectedRows = jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            // 处理空字符串，将其转换为null
            ps.setString(1, isEmpty(qc.getInitialThreshold()) ? null : qc.getInitialThreshold());
            ps.setString(2, isEmpty(qc.getDailyThreshold()) ? null : qc.getDailyThreshold());
            ps.setString(3, isEmpty(qc.getDailyThresholdReward()) ? null : qc.getDailyThresholdReward());
            ps.setString(4, isEmpty(qc.getRecommendMerchantsNum()) ? null : qc.getRecommendMerchantsNum());
            ps.setString(5, isEmpty(qc.getRecommendMerchantsReward()) ? null : qc.getRecommendMerchantsReward());
            ps.setString(6, isEmpty(qc.getMonthlyTechThreshold()) ? null : qc.getMonthlyTechThreshold());
            ps.setString(7, isEmpty(qc.getMonthlyTechRewardGold()) ? null : qc.getMonthlyTechRewardGold());
            ps.setString(8, isEmpty(qc.getMonthlyNoTechThreshold()) ? null : qc.getMonthlyNoTechThreshold());
            ps.setString(9, isEmpty(qc.getMonthlyNoTechPenaltyGold()) ? null : qc.getMonthlyNoTechPenaltyGold());
            ps.setString(10, isEmpty(qc.getDailyExtraQuantityUnit()) ? null : qc.getDailyExtraQuantityUnit());
            ps.setString(11, isEmpty(qc.getDailyExtraRewardGold()) ? null : qc.getDailyExtraRewardGold());
            ps.setString(12, isEmpty(qc.getOnOff()) ? null : qc.getOnOff());
            return ps;
        }, keyHolder);

        if (keyHolder.getKey() != null) {
            qc.setId(keyHolder.getKey().intValue()); // 设置自增ID到对象中
        }
        return affectedRows;
    }

    /**
     * 更新已存在的分量配置记录。
     * <p>
     * 根据传入的 QuantityConfig 对象的 id 字段来定位并更新数据库中的记录。
     * `updated_time` 字段由数据库自动更新。
     * </p>
     * @param qc 包含更新信息及目标记录ID的分量配置对象。
     * @return 受影响的数据库行数，通常为 1 表示成功。
     */
    public int update(QuantityConfigParam qc) {
        System.out.println(qc);
        String sql = "UPDATE " + TABLE_NAME + " SET initial_threshold = ?, daily_threshold = ?, daily_threshold_reward = ?, " +
                "recommend_merchants_num = ?, recommend_merchants_reward = ?, monthly_tech_threshold = ?, " +
                "monthly_tech_reward_gold = ?, monthly_no_tech_threshold = ?, monthly_no_tech_penalty_gold = ?, " +
                "daily_extra_quantity_unit = ?, daily_extra_reward_gold = ? ,on_off = ?" +
                "WHERE id = ?";
        
        return jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql);
            // 处理空字符串，将其转换为null
            ps.setString(1, isEmpty(qc.getInitialThreshold()) ? null : qc.getInitialThreshold());
            ps.setString(2, isEmpty(qc.getDailyThreshold()) ? null : qc.getDailyThreshold());
            ps.setString(3, isEmpty(qc.getDailyThresholdReward()) ? null : qc.getDailyThresholdReward());
            ps.setString(4, isEmpty(qc.getRecommendMerchantsNum()) ? null : qc.getRecommendMerchantsNum());
            ps.setString(5, isEmpty(qc.getRecommendMerchantsReward()) ? null : qc.getRecommendMerchantsReward());
            ps.setString(6, isEmpty(qc.getMonthlyTechThreshold()) ? null : qc.getMonthlyTechThreshold());
            ps.setString(7, isEmpty(qc.getMonthlyTechRewardGold()) ? null : qc.getMonthlyTechRewardGold());
            ps.setString(8, isEmpty(qc.getMonthlyNoTechThreshold()) ? null : qc.getMonthlyNoTechThreshold());
            ps.setString(9, isEmpty(qc.getMonthlyNoTechPenaltyGold()) ? null : qc.getMonthlyNoTechPenaltyGold());
            ps.setString(10, isEmpty(qc.getDailyExtraQuantityUnit()) ? null : qc.getDailyExtraQuantityUnit());
            ps.setString(11, isEmpty(qc.getDailyExtraRewardGold()) ? null : qc.getDailyExtraRewardGold());
            ps.setString(12, isEmpty(qc.getOnOff()) ? null : qc.getOnOff());
            ps.setInt(13, qc.getId());
            return ps;
        });
    }
}
