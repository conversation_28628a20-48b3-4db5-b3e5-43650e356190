package com.mall.project.dto.areaAuthorize;

import lombok.Data;

@Data
public class AreaAuthorize {

    private Integer id;               //授权记录ID
    private String enterpriseId;      //企业ID(关联cooperate_enterprise表id)
    private String areaId;            //授权区域ID
    private String phone;             //授权手机号
    private String startTime;         //授权开始时间
    private String endTime;           //授权结束时间
    private String proportion;        //授权比例
    private String onOff;             //省；市；县、区；镇、街，开关 0-开启 1-关闭
    private String level;             //授权级别 1-省 2-市 3-县、区 4-镇、街
    private String type;              //授权类型 C-授权 B-授权

    private Integer pageNum;
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
