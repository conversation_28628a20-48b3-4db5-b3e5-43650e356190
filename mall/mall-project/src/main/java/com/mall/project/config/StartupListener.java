package com.mall.project.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 应用启动监听器
 * 用于在应用启动完成后输出访问提示信息和执行检查操作
 */
@Component
@Slf4j
public class StartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Value("${server.port:9001}")
    private int serverPort;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("=== Mall Project 应用启动完成 ===");
        log.info("Java版本: {}", System.getProperty("java.version"));
        log.info("服务端口: {}", serverPort);
        log.info("服务发现: 已连接到 Nacos");
        log.info("数据库连接: 已建立 MySQL 连接池");
        log.info("缓存服务: 已连接到 Redis");
        log.info("🚀 Virtual Threads: 已启用 (Java 21特性)");
        log.info("");
        log.info("🚀 应用访问地址:");
        log.info("   健康检查: http://localhost:{}/api/health", serverPort);
        log.info("   模块管理: http://localhost:{}/modulesSet/modulesSet.html", serverPort);
        log.info("   API 接口: http://localhost:{}/api/**", serverPort);
        log.info("");
        log.info("📋 主要功能模块:");
        log.info("   用户管理: /api/user/**");
        log.info("   量化管理: /api/quantify/**");
        log.info("   数据导出: /api/export/**");
        log.info("   定时任务: 已启用并运行");
        log.info("");
        log.info("✅ 应用已就绪，可以正常访问！");

        // 检查关键组件是否正常
        try {
            log.info("🔍 所有组件检查完成，服务正常运行");
        } catch (Exception e) {
            log.error("❌ 启动后检查发现问题", e);
        }
    }
}
