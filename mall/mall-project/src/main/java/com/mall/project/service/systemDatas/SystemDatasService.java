package com.mall.project.service.systemDatas;

import java.util.Map;

/**
 * 系统数据服务接口
 */
public interface SystemDatasService {

    /**
     * 查询系统数据
     */
    public Map<String, Object> querySystemDatas();

    /**
     * 更新系统数据,这个由定时器执行,一般会在凌晨1点后执行
     */
    public void updateSystemDatas(String startTime);

    /**
     * 系统量化值累计
     */

    public String sysQuantityValue();

    /**
     * 系统信用值累计
     */
    public String sysCredit();

    /**
     * 系统促销金累计
     */
    public String sysPromotionGold();

    /**
     * 系统量化数累计
     */
    public String sysQuantityNum(String startTime);

    /**
     * 系统补贴金累计
     */
    public String sysSubsidyFunds();

    /**
     * 系统已核销补贴金累计
     */
    public String sysVerifiedSubsidy();

    /**
     * 系统分量累计
     */
    public String sysWeightTotal();
}