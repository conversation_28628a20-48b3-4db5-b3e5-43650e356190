package com.mall.project.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * 定时任务配置
 */
@Configuration
@EnableScheduling
public class SchedulingConfig implements SchedulingConfigurer {

    @Value("${task.scheduling.daily-task-cron}")
    private String dailyTaskCron;

    @Value("${task.scheduling.hourly-task-cron}")
    private String hourlyTaskCron;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        // 可以在这里添加更多配置，比如自定义线程池等
        // 如果需要在这里动态添加任务，可以使用taskRegistrar.addCronTask()方法
    }

    /**
     * 获取每日任务的cron表达式
     * @return cron表达式
     */
    public String getDailyTaskCron() {
        return dailyTaskCron;
    }

    /**
     * 获取凌晨1点任务的cron表达式
     * @return cron表达式
     */
    public String getHourlyTaskCron() {
        return hourlyTaskCron;
    }
}
