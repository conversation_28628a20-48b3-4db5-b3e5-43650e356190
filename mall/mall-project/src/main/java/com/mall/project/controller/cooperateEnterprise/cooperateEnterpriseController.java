package com.mall.project.controller.cooperateEnterprise;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.cooperateEnterprise.CooperateEnterpriseParam;
import com.mall.project.dto.cooperateEnterprise.zNHTradeData;
import com.mall.project.service.cooperateEnterprise.CooperateEnterpriseService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合作企业信息控制器
 * 处理与合作企业相关的HTTP请求，基础路径为/cooperateEnterprise
 */
@RestController
@Slf4j
@RequestMapping("/cooperateEnterprise")
public class cooperateEnterpriseController {


    @Autowired
    private CooperateEnterpriseService cooperateEnterpriseService;

    @GetMapping("/getAllCooperateEnterprise")
    public CommonResult<Map<String, Object>> getAllCooperateEnterprise() {
        Map<String, Object> cooperateEnterprises = cooperateEnterpriseService.getAllCooperateEnterprise();
        if (cooperateEnterprises != null) {
            return CommonResult.success(cooperateEnterprises);
        } else {
            return CommonResult.failed("获取合作企业信息失败");
        }
    }

    /**
     * 添加合作企业信息
     */
    @PostMapping("/addEnterpriseDataSet")
    public CommonResult<String> addEnterpriseDataSet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.addEnterpriseDataSet(param.getEnterpriseId());
        if (result > 0) {
            return CommonResult.success("添加合作企业信息成功");
        }else{
            return CommonResult.failed("添加合作企业信息失败");
        }
    }
    /**
     * 删除已设置参数的企业信息
     */
    @DeleteMapping("/deleteEnterpriseDataSet")
    public CommonResult<String> deleteEnterpriseDataSet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.deleteEnterpriseDataSet(param.getEnterpriseId());
        if (result > 0) {
            return CommonResult.success("删除成功");
        }else{
            return CommonResult.failed("删除失败");
        }
    }


    /**
     * 查看已设置参数的企业信息
     */
    @GetMapping("/getEnterpriseDataSet")
    public CommonResult<Map<String, Object>> getEnterpriseDataSet() {
        Map<String, Object> enterpriseDataSet = cooperateEnterpriseService.getEnterpriseDataSet();
        if (enterpriseDataSet != null) {
            return CommonResult.success(enterpriseDataSet);
        } else {
            return CommonResult.failed("获取企业设置信息失败");
        }
    }

    /**
     * 删除企业交易数据
     */
    @DeleteMapping("/deleteEnterpriseDataName")
    public CommonResult<String> deleteEnterpriseDataName(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.deleteEnterpriseDataName(param.getEnterpriseProductDataId());
        if (result > 0) {
            return CommonResult.success("删除成功");
        }else{
            return CommonResult.failed("删除失败");
        }
    }

    /**
     * 设置自定义常数
     */
    @PutMapping("/setCustomConstants")
    public CommonResult<String> setCustomConstants(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.setCustomConstants(param.getCustomConstants());
        if (result > 0) {
            return CommonResult.success("设置成功");
        }else{
            return CommonResult.failed("设置失败");
        }
    }

    /**
     * 查询自定义常数
     */
    @GetMapping("/getCustomConstants")
    public CommonResult<Map<String, Object>> getCustomConstants() {
        Map<String, Object> customConstants = cooperateEnterpriseService.getCustomConstants();
        if (customConstants != null) {
            return CommonResult.success(customConstants);
        }else{
            return CommonResult.failed("获取自定义常数失败");
        }
    }

    /**
     * 添加各企业系统每日更新总累计量化数企业名称
     */
    @PutMapping("/addEnterpriseQuantitySet")
    public CommonResult<String> addEnterpriseQuantitySet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.addEnterpriseQuantitySet(param.getEnterpriseId());
        if (result > 0) {
            return CommonResult.success("添加成功");
        }else{
            return CommonResult.failed("添加失败");
        }
    }

    /**
     * 删除已添加各企业系统每日更新总累计量化数企业名称
     */
    @DeleteMapping("/deleteEnterpriseQuantitySet")
    public CommonResult<String> deleteEnterpriseQuantitySet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.deleteEnterpriseQuantitySet(param.getEnterpriseId());
        if (result > 0) {
            return CommonResult.success("删除成功");
        }else{
            return CommonResult.failed("删除失败");
        }
    }
    /**
     * 查看已添加各企业系统每日更新总累计量化数企业名称
     */
    @GetMapping("/getEnterpriseQuantitySet")
    public CommonResult<Map<String, Object>> getEnterpriseQuantitySet() {
        Map<String, Object> enterpriseQuantitySet = cooperateEnterpriseService.getEnterpriseQuantitySet();
        if (enterpriseQuantitySet != null) {
            return CommonResult.success(enterpriseQuantitySet);
        }else{
            return CommonResult.failed("获取失败");
        }
    }

    /**
     * 删除各企业系统每日更新总累计量化数交易数据
     */
    @DeleteMapping("/deleteEnterpriseQuantityName")
    public CommonResult<String> deleteEnterpriseQuantityName(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.deleteEnterpriseQuantityName(param.getEnterpriseProductDataId());
        if (result > 0) {
            return CommonResult.success("删除成功");
        }else{
            return CommonResult.failed("删除失败");
        }
    }

    /**
     * 计算量化率 quantizationRate
     */
    @PostMapping("/getQuantizationRate")
    public CommonResult<Map<String, Object>>  quantizationRate(@RequestBody @Valid CooperateEnterpriseParam param) {
        Map<String,Object> quantizationRate = cooperateEnterpriseService.quantizationRate(param.getSearchMonth());
        if (quantizationRate != null) {
            return CommonResult.success(quantizationRate);
        }else{
            return CommonResult.failed("获取量化率失败");
        }
    }
    /**
     * 新增交易数据名称
     */
    @PutMapping("/tradeDataSet")
    public CommonResult<String> tradeDataSet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.tradeDataSet(param.getEnterpriseId(), param.getTradeName());
        if (result > 0) {
            return CommonResult.success("添加成功");
        }else{
            return CommonResult.failed("添加失败");
        }
    }
    /**
     * 查询交易数据名称
     */
    @GetMapping("/getTradeDataSet")
    public CommonResult<Map<String, Object>> tradeDataSet() {
        Map<String, Object> tradeDataSet = cooperateEnterpriseService.tradeDataSet();
        if (tradeDataSet != null) {
            return CommonResult.success(tradeDataSet);
        }else{
            return CommonResult.failed("获取失败");
        }
    }
    /**
     * 删除数据名称
     */
    @DeleteMapping("/deleteTradeDataSet")
    public CommonResult<String> deleteTradeDataSet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.deleteTradeDataSet(Long.parseLong(param.getId()));
        if (result > 0) {
            return CommonResult.success("删除成功");
        }else{
            return CommonResult.failed("删除失败");
        }
    }
    /**
     * 设置交易数据的每笔交易金额
     */
    @PutMapping("/tradeDataParameterSet")
    public CommonResult<String> tradeDataParameterSet(@RequestBody @Valid CooperateEnterpriseParam param) {
        int result = cooperateEnterpriseService.tradeDataParameterSet(param.getEnterpriseId(), param.getPerTradeAmount(),  param.getOnOff());
        if (result > 0) {
            return CommonResult.success("添加成功");
        }else{
            return CommonResult.failed("添加失败");
        }
    }
    /**
     * 查询设置交易数据的每笔交易金额
     */
    @PostMapping("/getTradeDataParameterSet")
    public CommonResult<Map<String, Object>>  QueryTradeDataParameterSet() {
        Map<String, Object> tradeDataParameterSet = cooperateEnterpriseService.QueryTradeDataParameterSet();
        if (tradeDataParameterSet != null) {
            return CommonResult.success(tradeDataParameterSet);
        }else{
            return CommonResult.failed("获取失败");
        }
    }
    /**
     *  获取询中南惠 交易数据明细 分页显示
     */
    @PostMapping("/QueryZNHTradeDataPages")
    public CommonResult<CommonPage<Map<String, Object>>> QueryZNHTradeDataPages(@RequestBody @Valid CooperateEnterpriseParam param) {
        CommonPage<Map<String, Object>> commonPage = cooperateEnterpriseService.QueryZNHTradeDataPages(param.getPhone(), param.getStartTime(), param.getEndTime(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    // 导出 中南惠 交易数据明细 Excel
    @PostMapping("/zNHTradeDataExport")
    public void zNHTradeDataExport(HttpServletResponse response,@RequestBody @Valid CooperateEnterpriseParam param) {
        try {
            // 获取累计交易数据
            List<Map<String, Object>> dataList = cooperateEnterpriseService.zNHTradeDataExport(param.getPhone(), param.getStartTime());
            String sumTradeAmount = String.valueOf(cooperateEnterpriseService.sumTradeAmount(param.getPhone(), param.getStartTime()));
            String sumTotalCount = String.valueOf(cooperateEnterpriseService.sumTotalCount(param.getPhone(), param.getStartTime()));

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateTime", "updateTime");
            fieldMapping.put("enterpriseId", "enterpriseId");
            fieldMapping.put("enterpriseName", "enterpriseName");
            fieldMapping.put("tradeName", "tradeName");
            fieldMapping.put("tradeAmount", "tradeAmount");
            fieldMapping.put("totalCount", "totalCount");

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总交易量")
                        .value(sumTradeAmount)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("中南惠今日总合计数")
                        .value(sumTotalCount)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<zNHTradeData> config = UniversalExcelExporter.ExportConfig.<zNHTradeData>builder()
                    .dataList(dataList)
                    .entityClass(zNHTradeData.class)
                    .fileName("交易数据明细")
                    .sheetName("交易数据明细")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出交易数据明细异常", e);
        }
    }
    /**
     * 企业交易数据读取, 开、关
     */
    @PostMapping("/setEnterpriseDataSwitch")
    public CommonResult<String> setEnterpriseDataSwitch(@RequestBody @Valid CooperateEnterpriseParam param){
        int result = cooperateEnterpriseService.setEnterpriseDataSwitch(param.getEnterpriseDataSwitch());
        if (result > 0) {
            return CommonResult.success("设置成功");
        }else{
            return CommonResult.failed("设置失败");
        }
    }
    /**
     * 各企业系统每日更新总累计量化数 开、关
     */
    @PostMapping("/setQuantityDataSwitch")
    public CommonResult<String> setQuantityDataSwitch(@RequestBody @Valid CooperateEnterpriseParam param){
        int result = cooperateEnterpriseService.setQuantityDataSwitch(param.getQuantityDataSwitch());
        if (result > 0) {
            return CommonResult.success("设置成功");
        }else{
            return CommonResult.failed("设置失败");
        }
    }
    /**
     * 自定义常数开、关
     */
    @PostMapping("/setCustomConstantsSwitch")
    public CommonResult<String> setCustomConstantsSwitch(@RequestBody @Valid CooperateEnterpriseParam param){
        int result = cooperateEnterpriseService.setCustomConstantsSwitch(param.getCustomConstantsSwitch());
        if (result > 0) {
            return CommonResult.success("设置成功");
        }else{
            return CommonResult.failed("设置失败");
        }
    }
}
