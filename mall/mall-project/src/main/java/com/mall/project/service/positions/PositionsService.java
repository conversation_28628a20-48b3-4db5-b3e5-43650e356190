package com.mall.project.service.positions;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

public interface PositionsService {
    Map<String, Object> getPositions();
    
    /**
     * 获取所有岗位数据，用于Excel导出
     */
    List<Map<String, Object>> getPositionsForExport();

    /**
     * Retrieves a paginated list of positions.
     *
     * @param searchName The name to search for (currently unused).
     * @param pageNum    The page number to retrieve.
     * @param pageSize   The number of items per page.
     * @return A {@link CommonPage} containing the paginated list of positions.
     */
    CommonPage<Map<String, Object>> getPositionsPaged(String searchName, int pageNum, int pageSize);
}
