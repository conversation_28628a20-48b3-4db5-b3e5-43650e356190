package com.mall.project.controller.partnerEnterpriseAdminData;


import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.partnerEnterpriseAdminData.PartnerEnterpriseAdminData;
import com.mall.project.service.partnerEnterpriseAdminData.PartnerEnterpriseAdminDataService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 所有合作企业Admain的各ID每日每笔数据量化数比控制器
 */
@RestController
public class PartnerEnterpriseAdminDataController {
    @Autowired
    public PartnerEnterpriseAdminDataService partnerEnterpriseAdminDataService;

    @GetMapping("/getPartnerEnterpriseAdminData")
    public CommonResult<Map<String, Object>> getDailyTradePercentage() {
        Map<String, Object> dailyTradePercentage = partnerEnterpriseAdminDataService.getPartnerEnterpriseAdminData();
        if (!dailyTradePercentage.isEmpty()) {
            return CommonResult.success(dailyTradePercentage);
        }else{
            return CommonResult.failed("暂无数据！");
        }
    }

    @PutMapping("/saveOrUpdatePartnerEnterpriseAdminData")
    public CommonResult<Map<String, Object>> saveOrUpdateDailyTradePercentage(@RequestBody @Valid PartnerEnterpriseAdminData pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = partnerEnterpriseAdminDataService.saveOrUpdatePartnerEnterpriseAdminData(pojo,Integer.parseInt(userInfo.get("id").toString()));
        if (!dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }
}
