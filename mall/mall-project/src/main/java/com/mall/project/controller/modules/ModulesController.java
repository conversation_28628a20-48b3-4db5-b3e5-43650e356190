package com.mall.project.controller.modules;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.user.UserRegisterParam;
import com.mall.project.service.modules.ModulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class ModulesController {

    @Autowired
    private ModulesService modulesService;

    /**
     * 获取所有模块
     */
    @GetMapping("/modules")
    public CommonResult<Map<String, Object>> getModules() {
        Map<String, Object> modules = modulesService.getModules();
        return modules != null ? CommonResult.success(modules) : CommonResult.failed("获取模块失败");
    }

    /**
     * 获取用户有授权的模块
     */
    @PostMapping("/modules/user")
    public CommonResult<Map<String, Object>> getUserModules(@RequestBody UserRegisterParam param, @CurrentUser Map<String, Object> userInfo) {
        if (userInfo == null) {
            return CommonResult.failed("未登录或token无效");
        }
        long employeeId;
        if (param.getEmployeeId() == null){
            employeeId = Long.parseLong(userInfo.get("id").toString());
        }else{
            employeeId = param.getEmployeeId();
        }
        Map<String, Object> modules = modulesService.getUserModules(employeeId);
        return modules != null ? CommonResult.success(modules) : CommonResult.failed("获取模块失败");
    }
    /**
     * 获取用户有授权的模块树
     */
    @PostMapping("/modules/getUserModulesTree")
    public CommonResult<Map<String, Object>> getUserModulesTree(@CurrentUser Map<String, Object> userInfo) {
        if (userInfo == null) {
            return CommonResult.failed("未登录或token无效");
        }
        Map<String, Object> modules = modulesService.getUserModulesTree(Long.parseLong(userInfo.get("id").toString()));
        return modules != null ? CommonResult.success(modules) : CommonResult.failed("获取模块失败");
    }
}
