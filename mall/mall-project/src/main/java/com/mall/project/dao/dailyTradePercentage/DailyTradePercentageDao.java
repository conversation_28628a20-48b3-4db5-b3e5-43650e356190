package com.mall.project.dao.dailyTradePercentage;

import com.mall.project.dto.dailyTradePercentage.DailyTradePercentage;
import com.mall.project.util.PreparedStatementUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 所有企业各ID每日每笔新交易数据的量化数比配置
 */

@Repository
@Slf4j
public class DailyTradePercentageDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public Map<String,Object> getDailyTradePercentage(){
        String sql = "SELECT is_enabled,daily_trade_percentage,ranking1,ranking1_percentage,ranking2,ranking2_percentage FROM daily_trade_percentage WHERE id = 1";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (EmptyResultDataAccessException e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }

    /**
     * 判断用户层级
     * 用户层级关系查询业务
     * 核心功能
     * 这个SQL实现了一个用户层级关系向上追溯查询的业务逻辑，主要用于在B端用户体系中查找指定用户的上级层级关系。
     * 业务逻辑说明
     * 起始点：从指定手机号的用户开始作为Level 0（自己）
     * 向上递归：通过parent_id字段向上查找父级用户，构建完整的上级层级链条
     * Level 0：查询起始用户（自己）
     * Level 1：直接上级
     * Level 2：上级的上级
     * Level 3：上上级的上级
     * 以此类推...
     * 用户过滤：只查询符合条件的B端用户
     * 状态正常（status = 0）
     * 链类型为B（chain_type = 'B'）
     * 用户类型为B或CB（user_type = 'B' 或 user_type = 'CB'）
     * 营业执照去重：关键业务规则
     * 如果多个用户使用相同的营业执照（business_license）
     * 只保留其中注册时间最早的用户（create_time最小）
     * 过滤掉同营业执照下的其他用户
     * 层级重新计算：去重后重新计算层级编号，保持层级关系的连续性
     * 结果筛选：返回指定层级（如Level 3）的用户信息
     */
    public Map<String, Object> judgeUserLevel(String phone, Integer ranking){
        String mode = "B";
        Integer targetLevel = ranking; // 使用ranking作为目标层级
        Integer maxDepth = 100;

        String sql = "WITH RECURSIVE " +
                "start_users AS ( " +
                "  SELECT " +
                "    u.*, " +
                "    0 AS depth, " +
                "    JSON_ARRAY(CAST(u.id AS CHAR)) AS path_json, " +
                "    u.id AS root_id " +
                "  FROM mall_b_users u " +
                "  WHERE u.phone COLLATE utf8mb4_general_ci = ? COLLATE utf8mb4_general_ci " +
                "), " +
                "chain AS ( " +
                "  SELECT * FROM start_users " +
                "  UNION ALL " +
                "  SELECT " +
                "    p.*, " +
                "    c.depth + 1 AS depth, " +
                "    JSON_ARRAY_APPEND(c.path_json, '$', CAST(p.id AS CHAR)) AS path_json, " +
                "    c.root_id AS root_id " +
                "  FROM chain c " +
                "  JOIN mall_b_users p ON p.id = c.parent_id " +
                "  WHERE " +
                "    c.depth + 1 <= COALESCE(?, 100) " +
                "    AND JSON_CONTAINS(c.path_json, JSON_QUOTE(CAST(p.id AS CHAR)), '$') = 0 " +
                "), " +
                "chain_with_flag AS ( " +
                "  SELECT " +
                "    c.*, " +
                "    CASE " +
                "      WHEN UPPER(?) = 'C' THEN IF(c.user_type = 'C', 1, 0) " +
                "      WHEN UPPER(?) = 'B' THEN IF(c.user_type IN ('B','CB'), 1, 0) " +
                "      ELSE 0 " +
                "    END AS type_flag, " +
                "    CASE " +
                "      WHEN COALESCE(TRIM(c.business_license), '') = '' THEN CONCAT('UNIQUE_', c.id) " +
                "      ELSE c.business_license " +
                "    END AS group_key " +
                "  FROM chain c " +
                "), " +
                "mode_group_leaders AS ( " +
                "  SELECT " +
                "    cwf.root_id, " +
                "    cwf.group_key, " +
                "    cwf.id         AS leader_id, " +
                "    cwf.depth      AS leader_depth, " +
                "    cwf.create_time AS leader_ct, " +
                "    cwf.id         AS leader_sort_id " +
                "  FROM ( " +
                "    SELECT " +
                "      cwf.*, " +
                "      ROW_NUMBER() OVER ( " +
                "        PARTITION BY cwf.root_id, cwf.group_key " +
                "        ORDER BY cwf.depth ASC, cwf.create_time ASC, cwf.id ASC " +
                "      ) AS rn " +
                "    FROM chain_with_flag cwf " +
                "    WHERE cwf.type_flag = 1 " +
                "  ) cwf " +
                "  WHERE cwf.rn = 1 " +
                "), " +
                "mode_group_levels AS ( " +
                "  SELECT " +
                "    mgl.root_id, " +
                "    mgl.group_key, " +
                "    ROW_NUMBER() OVER ( " +
                "      PARTITION BY mgl.root_id " +
                "      ORDER BY mgl.leader_depth ASC, mgl.leader_ct ASC, mgl.leader_sort_id ASC " +
                "    ) - 1 AS level_idx " +
                "  FROM mode_group_leaders mgl " +
                "), " +
                "final_nodes AS ( " +
                "  SELECT " +
                "    cwf.*, " +
                "    mgl.level_idx AS final_level_num " +
                "  FROM chain_with_flag cwf " +
                "  LEFT JOIN mode_group_levels mgl " +
                "    ON mgl.root_id = cwf.root_id " +
                "   AND mgl.group_key = cwf.group_key " +
                ") " +
                "SELECT " +
                "  fn.phone, " +
                "  fn.username, " +
                "  fn.parent_username, " +
                "  fn.user_type, " +
                "  fn.status, " +
                "  fn.flag, " +
                "  fn.jurisdiction, " +
                "  fn.fans, " +
                "  fn.address, " +
                "  fn.login_address, " +
                "  fn.business_license, " +
                "  fn.create_time, " +
                "  CASE WHEN fn.type_flag = 1 THEN CAST(fn.final_level_num AS CHAR) ELSE '-' END AS `level` " +
                "FROM final_nodes fn " +
                "WHERE " +
                "  ( " +
                "    ? IS NULL " +
                "    OR (fn.type_flag = 1 AND fn.final_level_num = ?) " +
                "    OR (fn.type_flag = 0) " +
                "  ) " +
                "   AND fn.final_level_num = ? " +
                "ORDER BY " +
                "  fn.depth ASC, " +
                "  CASE WHEN fn.type_flag = 1 THEN 0 ELSE 1 END ASC, " +
                "  fn.create_time ASC, " +
                "  fn.id ASC";

        Map<String, Object> levelDataMap = new HashMap<>();
        try {
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql,
                phone,           // 起点手机号
                maxDepth,        // 最大层级
                mode,            // 模式 'C' 或 'B'
                mode,            // 模式 'C' 或 'B' (重复使用)
                targetLevel,     // 目标层级判断
                targetLevel,     // 目标层级判断 (重复使用)
                targetLevel      // 最终层级过滤
            );
            if (rows == null || rows.isEmpty()) {
                return levelDataMap; // 空Map
            }
            if (rows.size() == 1) {
                return rows.get(0);
            }
            // 多条时，取 create_time 最早的一条
            Map<String, Object> earliest = rows.get(0);
            Object earliestCtObj = earliest.get("create_time");
            Timestamp earliestTs = earliestCtObj instanceof Timestamp ? (Timestamp) earliestCtObj : null;
            for (int i = 1; i < rows.size(); i++) {
                Map<String, Object> row = rows.get(i);
                Object ctObj = row.get("create_time");
                Timestamp ts = ctObj instanceof Timestamp ? (Timestamp) ctObj : null;
                if (earliestTs == null || (ts != null && ts.before(earliestTs))) {
                    earliest = row;
                    earliestTs = ts;
                }
            }
            return earliest;
        } catch (EmptyResultDataAccessException e) {
            // 查询结果为空时返回空Map
            return levelDataMap;
        }
    }

    /**
     * 保存或更新所有企业各ID每日每笔新交易数据的量化数比配置
     */
    public int saveOrUpdateDailyTradePercentage(DailyTradePercentage pojo, Integer updatePerson){
        try {
            String sql = "INSERT INTO daily_trade_percentage(id, is_enabled, daily_trade_percentage, ranking1, ranking1_percentage, ranking2, ranking2_percentage, update_person, update_time) VALUES (1, ?, ?, ?, ?, ?, ?, ?, NOW()) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "is_enabled = VALUES(is_enabled), daily_trade_percentage = VALUES(daily_trade_percentage), ranking1 = VALUES(ranking1), ranking1_percentage = VALUES(ranking1_percentage), ranking2 = VALUES(ranking2), ranking2_percentage = VALUES(ranking2_percentage), update_time = NOW()";
            jdbcTemplate.update(connection -> {
                var ps = connection.prepareStatement(sql);
                try {
                    PreparedStatementUtil.setPreparedStatementParams(ps, pojo, updatePerson);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return ps;
            });
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }
}
