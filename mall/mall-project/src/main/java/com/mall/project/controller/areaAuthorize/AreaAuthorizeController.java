package com.mall.project.controller.areaAuthorize;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.areaAuthorize.AreaAuthorize;
import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api")
public class AreaAuthorizeController {

    @Autowired
    private AreaAuthorizeService areaAuthorizeService;

    // 获取区域
    @PostMapping("/getArea")
    public CommonResult<Map<String, Object>> getArea(@RequestBody @Valid AreaAuthorize pojo) {
        Map<String, Object> dataMap = areaAuthorizeService.getArea(pojo.getAreaId());
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("获取区域失败！");
        }
    }
    /**
     * 获取合作企业
     */
    @GetMapping("/getCooperateEnterprise")
    public CommonResult<Map<String, Object>> getCooperateEnterprise() {
        Map<String, Object> dataMap = areaAuthorizeService.getCooperateEnterprise();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("获取合作企业失败！");
        }
    }
    // 对手机号授权
    @PostMapping("/saveAreaAuthorize")
    public CommonResult<String> saveAreaAuthorize(@RequestBody @Valid AreaAuthorize pojo, @CurrentUser Map<String, Object> userInfo) {
        int result = areaAuthorizeService.saveAreaAuthorize(pojo, Integer.parseInt(userInfo.get("id").toString()));
        if (result > 0) {
            return CommonResult.success("授权成功！");
        }else if(result == -1){
            return CommonResult.failed("该手机号已经授权，不能重复授权！");
        }else if(result == -2){
            return CommonResult.failed("该区域已经授权，不能重复授权！");
        }else if(result == -3){
            return CommonResult.failed("该手机号不存在，不能授权！");
        }else if(result == -4){
            return CommonResult.failed("该手机号的用户类型与授权类型不符，不能授权！用户类型为B的可以进行B授权, 用户类型为C的不能进行C授权,只有用户类型为CB的才能进行C授权");
        }else {
            return CommonResult.failed("授权失败！");
        }
    }
    // 查询授权信息
    @PostMapping("/queryAreaAuthorize")
    public CommonResult<CommonPage<Map<String, Object>>> queryAreaAuthorize(@RequestBody @Valid AreaAuthorize pojo) {
        CommonPage<Map<String, Object>> commonPage = areaAuthorizeService.queryAreaAuthorize(pojo.getType(),pojo.getLevel(), pojo.getPageNum(), pojo.getPageSize());
        return CommonResult.success(commonPage);
    }

    // 删除授权信息, 根据表id 删除
    @PostMapping("/deleteAreaAuthorize")
    public CommonResult<String> deleteAreaAuthorize(@RequestBody @Valid AreaAuthorize pojo) {
        int result = areaAuthorizeService.deleteAreaAuthorize(pojo.getId());
        if (result > 0) {
            return CommonResult.success("删除授权成功！");
        } else {
            return CommonResult.failed("删除授权失败！");
        }
    }
}
