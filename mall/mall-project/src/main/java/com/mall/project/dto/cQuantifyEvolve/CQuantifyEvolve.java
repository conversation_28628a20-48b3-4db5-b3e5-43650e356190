package com.mall.project.dto.cQuantifyEvolve;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * C量化值进化量实体类
 */
@Data
public class CQuantifyEvolve {

    @ExcelProperty("日期")
    private String updateDate;             //日期
    @ExcelProperty("手机号")
    private String phone;                  //手机号
    @ExcelProperty("名字")
    private String userName;               //用户名
    @ExcelProperty("今日量化值进化量")
    private String cQuantifyEvolve;         //今日量化值进化量
    @ExcelProperty("累计量化值进化量")
    private String cQuantifyEvolveTotal;    //累计量化值进化量

    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;              //查询开始时间
    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;                //查询结束时间

    @ExcelIgnore  //查询大于0的字段          // 1.今日量化值进化量  2.累计量化值进化量
    private Integer isGreaterThanZero;

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
