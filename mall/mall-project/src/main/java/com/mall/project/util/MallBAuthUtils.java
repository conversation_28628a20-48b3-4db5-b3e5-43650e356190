package com.mall.project.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.project.exception.BusinessException;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * mallB系统认证工具类
 * 提供与mallB系统交互的认证功能
 */
@Component
@Slf4j
public class MallBAuthUtils {

    /**
     * -- GETTER --
     *  获取mallB系统基础URL
     *
     */
    @Getter
    @Value("${mall-b.api.base-url}")
    private String mallBBaseUrl;

    @Value("${mall-b.api.username}")
    private String mallBUsername;

    @Value("${mall-b.api.password}")
    private String mallBPassword;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // Redis中存储token的key
    private static final String MALL_B_TOKEN_KEY = "mall:b:auth:token";
    
    // token默认过期时间（秒）- 比实际过期时间短一些，确保安全边界
    private static final long TOKEN_EXPIRE_SECONDS = 86400; // 24小时
    
    // 用于解析JSON响应
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取mallB系统的认证Token
     * 首先尝试从Redis获取，如果不存在或已过期则重新登录获取
     * @return 认证Token字符串
     * @throws BusinessException 当认证失败时抛出异常
     */
    public String getMallBAuthToken() throws BusinessException {
        // 首先尝试从Redis获取token
        String token = redisTemplate.opsForValue().get(MALL_B_TOKEN_KEY);
        
        // 如果Redis中存在有效token，直接返回
        if (token != null && !token.isEmpty()) {
            log.debug("从Redis获取mallB系统token成功");
            return token;
        }
        
        // Redis中不存在有效token，重新登录获取
        log.info("Redis中不存在有效token，重新登录mallB系统获取token");
        return loginAndGetToken();
    }
    
    /**
     * 登录mallB系统并获取新token
     * @return 新的认证Token字符串
     * @throws BusinessException 当认证失败时抛出异常
     */
    private String loginAndGetToken() throws BusinessException {
        try {
            // 创建登录请求客户端
            RestTemplate restTemplate = new RestTemplate();
            ObjectMapper objectMapper = new ObjectMapper();

            // 登录mallB系统
            String loginUrl = mallBBaseUrl + "/ASystemLogin";

            // 准备登录参数 - 使用JSON格式
            HttpHeaders loginHeaders = new HttpHeaders();
            loginHeaders.setContentType(MediaType.APPLICATION_JSON);

            // 创建JSON格式的登录请求体 - 使用Map和ObjectMapper
            Map<String, String> loginRequest = new HashMap<>();
            loginRequest.put("username", mallBUsername);
            loginRequest.put("password", mallBPassword);

            HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(loginRequest), loginHeaders);

            // 发送登录请求
            ResponseEntity<String> loginResponse = restTemplate.postForEntity(loginUrl, requestEntity, String.class);

            // 打印响应状态和内容以便调试
            log.info("MallB Login status code: {}", loginResponse.getStatusCode());
            log.debug("MallB Login response body: {}", loginResponse.getBody());

            // 检查登录是否成功
            if (loginResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("登录mallB系统失败: " + loginResponse.getStatusCode());
            }

            // 解析JSON响应获取token
            String responseBody = loginResponse.getBody();
            if (responseBody == null) {
                throw new BusinessException("登录mallB系统响应为空");
            }

            JsonNode rootNode = objectMapper.readTree(responseBody);

            if (rootNode.get("code").asInt() != 200) {
                throw new BusinessException("登录mallB系统失败: " + rootNode.get("msg").asText());
            }

            String token = rootNode.path("data").path("token").asText();
            if (token == null || token.isEmpty()) {
                throw new BusinessException("未能获取mallB系统的认证token");
            }
            
            // 将token存入Redis，并设置过期时间
            redisTemplate.opsForValue().set(MALL_B_TOKEN_KEY, token, TOKEN_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("成功获取mallB系统token并存入Redis，过期时间: {}秒", TOKEN_EXPIRE_SECONDS);
            
            return token;
        } catch (Exception e) {
            log.error("mallB系统认证失败: {}", e.getMessage(), e);
            throw new BusinessException("mallB系统认证失败: " + e.getMessage());
        }
    }
    
    /**
     * 强制刷新token，无论当前token是否有效
     * @return 新的认证Token字符串
     * @throws BusinessException 当认证失败时抛出异常
     */
    public String refreshToken() throws BusinessException {
        // 删除Redis中的旧token
        redisTemplate.delete(MALL_B_TOKEN_KEY);
        // 重新登录获取新token
        return loginAndGetToken();
    }
    
    /**
     * 创建带有认证令牌的HTTP头
     * @return 包含认证信息的HttpHeaders对象
     * @throws BusinessException 当认证失败时抛出异常
     */
    public HttpHeaders getAuthenticatedHeaders() throws BusinessException {
        String token = getMallBAuthToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(HttpHeaders.AUTHORIZATION, "Bearer " + token);
        return headers;
    }
    
    /**
     * 创建带认证的HTTP请求实体
     * @param body 请求体内容，可以为null
     * @return 包含认证信息的HttpEntity对象
     * @throws BusinessException 当认证失败时抛出异常
     */
    public <T> HttpEntity<T> createAuthenticatedEntity(T body) throws BusinessException {
        return new HttpEntity<>(body, getAuthenticatedHeaders());
    }
    
    /**
     * 发送GET请求到mallB系统API，并返回响应（无请求参数）
     * @param endpoint API端点路径（不包含基础URL）
     * @param responseType 响应类型的Class对象
     * @return 响应实体
     * @throws BusinessException 当请求失败时抛出异常
     */
    public <T> ResponseEntity<T> getForEntity(String endpoint, Class<T> responseType) throws BusinessException {
        try {
            String url = mallBBaseUrl + endpoint;
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<?> entity = createAuthenticatedEntity(null);
            
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
            return handleResponse(response, endpoint, null, responseType);
        } catch (Exception e) {
            log.error("调用mallB系统API失败: {}", e.getMessage(), e);
            throw new BusinessException("调用mallB系统API失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送GET请求到mallB系统API，并返回响应（带JSON请求体）
     * 注意：此方法实际上发送的是带有请求体的GET请求，一些服务器可能不支持带有请求体的GET请求
     * 如果服务器不支持，可能需要将请求转换为POST或者使用URL参数
     * @param endpoint API端点路径（不包含基础URL）
     * @param requestBody 请求体对象
     * @param responseType 响应类型的Class对象
     * @return 响应实体
     * @throws BusinessException 当请求失败时抛出异常
     */
    public <T, R> ResponseEntity<R> getForEntity(String endpoint, T requestBody, Class<R> responseType) throws BusinessException {
        try {
            String url = mallBBaseUrl + endpoint;
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<T> entity = createAuthenticatedEntity(requestBody);
            
            ResponseEntity<R> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
            return handleResponse(response, endpoint, requestBody, responseType);
        } catch (Exception e) {
            log.error("调用mallB系统API失败: {}", e.getMessage(), e);
            throw new BusinessException("调用mallB系统API失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送GET请求到mallB系统API，并返回响应（带URL参数）
     * 此方法将参数添加到URL查询字符串中，而不是请求体中
     * @param endpoint API端点路径（不包含基础URL）
     * @param urlParams URL参数Map
     * @param responseType 响应类型的Class对象
     * @return 响应实体
     * @throws BusinessException 当请求失败时抛出异常
     */
    public <T> ResponseEntity<T> getForEntityWithParams(String endpoint, Map<String, Object> urlParams, Class<T> responseType) throws BusinessException {
        try {
            // 构建带参数的URL
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(mallBBaseUrl + endpoint);
            if (urlParams != null) {
                for (Map.Entry<String, Object> entry : urlParams.entrySet()) {
                    builder.queryParam(entry.getKey(), entry.getValue());
                }
            }
            String url = builder.toUriString();
            
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<?> entity = createAuthenticatedEntity(null);
            
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
            return handleResponse(response, url, null, responseType);
        } catch (Exception e) {
            log.error("调用mallB系统API失败: {}", e.getMessage(), e);
            throw new BusinessException("调用mallB系统API失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送POST请求到mallB系统API，并返回响应
     * @param endpoint API端点路径（不包含基础URL）
     * @param requestBody 请求体对象
     * @param responseType 响应类型的Class对象
     * @return 响应实体
     * @throws BusinessException 当请求失败时抛出异常
     */
    public <T, R> ResponseEntity<R> postForEntity(String endpoint, T requestBody, Class<R> responseType) throws BusinessException {
        try {
            String url = mallBBaseUrl + endpoint;
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<T> entity = createAuthenticatedEntity(requestBody);
            
            ResponseEntity<R> response = restTemplate.postForEntity(url, entity, responseType);
            return handleResponse(response, endpoint, requestBody, responseType);
        } catch (Exception e) {
            log.error("调用mallB系统API失败: {}", e.getMessage(), e);
            throw new BusinessException("调用mallB系统API失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理API响应，检查是否需要刷新token并重试
     * @param response 初始响应
     * @param endpoint API端点
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @return 处理后的响应
     * @throws BusinessException 当请求失败时抛出异常
     */
    private <T, R> ResponseEntity<R> handleResponse(ResponseEntity<R> response, String endpoint, T requestBody, Class<R> responseType) throws BusinessException {
        // 检查响应状态码，401表示未授权，可能是token过期
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
            log.info("mallB系统API返回未授权状态码，尝试刷新token并重试请求");
            
            // 刷新token
            refreshToken();
            
            // 重新创建请求实体并重试请求
            String url = mallBBaseUrl;
            if (!endpoint.startsWith("http")) {
                url += endpoint; // 如果endpoint不是完整URL，添加基础URL
            } else {
                url = endpoint; // 如果endpoint是完整URL（如在getForEntityWithParams中），直接使用
            }
            
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<T> entity = createAuthenticatedEntity(requestBody);
            
            if (requestBody == null) {
                // GET请求
                return restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
            } else {
                // POST请求或带请求体的GET请求
                if (endpoint.contains("?")) {
                    // 如果URL包含查询参数，表示是带参数的GET请求
                    return restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
                } else {
                    // 否则是POST请求
                    return restTemplate.postForEntity(url, entity, responseType);
                }
            }
        }
        
        // 检查响应内容是否为String类型，并且包含JSON格式的内容
        if (response.getBody() instanceof String) {
            try {
                String responseBody = (String) response.getBody();
                JsonNode rootNode = objectMapper.readTree(responseBody);
                
                // 检查响应中的code字段，401表示认证失败
                if (rootNode.has("code") && rootNode.get("code").asInt() == 401 && 
                    rootNode.has("msg") && rootNode.get("msg").asText().contains("认证失败")) {
                    
                    log.info("mallB系统API返回认证失败响应，尝试刷新token并重试请求: {}", responseBody);
                    
                    // 刷新token
                    refreshToken();
                    
                    // 重新创建请求实体并重试请求
                    String url = mallBBaseUrl;
                    if (!endpoint.startsWith("http")) {
                        url += endpoint; // 如果endpoint不是完整URL，添加基础URL
                    } else {
                        url = endpoint; // 如果endpoint是完整URL（如在getForEntityWithParams中），直接使用
                    }
                    
                    RestTemplate restTemplate = new RestTemplate();
                    HttpEntity<T> entity = createAuthenticatedEntity(requestBody);
                    
                    if (requestBody == null) {
                        // GET请求
                        return restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
                    } else {
                        // POST请求或带请求体的GET请求
                        if (endpoint.contains("?")) {
                            // 如果URL包含查询参数，表示是带参数的GET请求
                            return restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
                        } else {
                            // 否则是POST请求
                            return restTemplate.postForEntity(url, entity, responseType);
                        }
                    }
                }
            } catch (Exception e) {
                // 解析JSON失败，忽略并继续处理
                log.debug("解析响应内容失败，继续处理: {}", e.getMessage());
            }
        }
        
        return response;
    }
} 