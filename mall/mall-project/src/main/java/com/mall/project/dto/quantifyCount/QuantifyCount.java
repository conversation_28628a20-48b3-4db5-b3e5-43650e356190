package com.mall.project.dto.quantifyCount;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 描述：量化数实体类
 */
@Data
public class QuantifyCount {


    @ExcelProperty("时间")
    private String updateTime;             //量化数更新时间

    @ExcelProperty("手机号")
    private String phone;                  //手机号

    @ExcelProperty("名字（名称）")
    private String userName;               //用户名

    @ExcelProperty("今日量化数")
    private String todayQuantifyCount;     //今日量化数

    @ExcelProperty("今日分量")
    private String todayWeightCount;       //今日分量

    @ExcelProperty("累计分量")
    private String totalWeightCount;       //累计分量

    @ExcelProperty("累计量化数")
    private String totalQuantifyCount;     //累计量化数

    @ExcelIgnore  //查询大于0的字段          // 1.今日量化数 2. 今日分量  3. 累计分量  4. 累计量化数
    private Integer isGreaterThanZero;

    @ExcelIgnore  //Excel导出时忽略此字段
    private String startTime;              //查询开始时间

    @ExcelIgnore  //Excel导出时忽略此字段
    private String endTime;                //查询结束时间

    @ExcelIgnore  //Excel导出时忽略此字段
    private String isEnabled;              //量化数设置开、关
    @ExcelIgnore  //Excel导出时忽略此字段
    private String proportion;             //量化数设置比例

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
