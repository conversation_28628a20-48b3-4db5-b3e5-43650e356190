package com.mall.project.dto.writeOffData;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 核销数据实体类
 */
@Data
public class WriteOffData {

    @ExcelProperty("日期")
    private String updateDate;
    @ExcelProperty("手机号")
    private String phone;
    @ExcelProperty("今日核销补贴金")
    private String writeOffSubsidy;
    @ExcelProperty("累计核销补贴金")
    private String writeOffSubsidyTotal;
    @ExcelProperty("未核销")
    private String unWriteOffSubsidy;
    @ExcelProperty("已核销促销金")
    private String promotionUsed;
    @ExcelProperty("累计使用金额")
    private String totalPromotionUsed;
    @ExcelProperty("核销金")
    private String writeOffGold;

    @ExcelIgnore  //查询大于0的字段          // 1. 今日核销补贴金  2. 累计核销补贴金   3. 未核销  4. 已核销促销金   5. 累计使用金额    6. 核销值
    private Integer isGreaterThanZero;

    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;              //查询开始时间
    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;                //查询结束时间

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
