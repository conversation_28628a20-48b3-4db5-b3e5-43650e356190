package com.mall.project.service.partnerEnterpriseAdminData.impl;

import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.partnerEnterpriseAdminData.PartnerEnterpriseAdminDataDao;
import com.mall.project.dto.partnerEnterpriseAdminData.PartnerEnterpriseAdminData;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.partnerEnterpriseAdminData.PartnerEnterpriseAdminDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 所有合作企业Admain的各ID每日每笔数据量化数比接口服务实现类
 */
@Service
@Slf4j
public class PartnerEnterpriseAdminDataServiceImpl implements PartnerEnterpriseAdminDataService {

    @Autowired
    private PartnerEnterpriseAdminDataDao partnerEnterpriseAdminDataDao;

    @Override
    public Map<String, Object> getPartnerEnterpriseAdminData() {
        Map<String, Object> dataMap = partnerEnterpriseAdminDataDao.getPartnerEnterpriseAdminData();
        // 转换下划线格式为驼峰格式
        return ConvertToCamelCase.convertToCamelCase(dataMap);
    }

    @Override
    public Map<String, Object> saveOrUpdatePartnerEnterpriseAdminData(PartnerEnterpriseAdminData pojo, Integer updatePerson) {
        if(pojo.getIsEnabled() == null || pojo.getIsEnabled().isEmpty() || !pojo.getIsEnabled().matches("^[01]$")) {
            throw new BusinessException("所有企业各ID每日每笔新交易数据的量化数比开、关不能为空，且只能为0或1");
        }
        if(isNotEmpty(pojo.getDailyDataPercentage()) && !pojo.getDailyDataPercentage().matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("所有企业各ID每日每笔新交易数据的量化数比只能为正整数或小数，且最多保留4位小数");
        }
        int result = partnerEnterpriseAdminDataDao.saveOrUpdatePartnerEnterpriseAdminData(pojo, updatePerson);
        if(result > 0){
            return getPartnerEnterpriseAdminData();
        }else{
            return null;
        }
    }
}
