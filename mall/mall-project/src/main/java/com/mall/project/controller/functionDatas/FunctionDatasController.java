package com.mall.project.controller.functionDatas;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.functionDatas.FunctionDatas;
import com.mall.project.service.functionDatas.FunctionDatasService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 功能数据控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class FunctionDatasController {


    @Autowired
    private FunctionDatasService functionDatasService;

    /**
     * 新增功能数据
     */
    @PutMapping("/addFunctionDatas")
    public CommonResult<String> addFunctionDatas(@RequestBody @Valid FunctionDatas pojo, @CurrentUser Map<String, Object> userInfo) {
            int result = functionDatasService.addFunctionDatas(pojo, Integer.parseInt(userInfo.get("id").toString()));
            if (result > 0) {
                return CommonResult.success("新增成功！");
            }else {
                return CommonResult.failed("新增失败！");
            }
        }

    /**
     * 查询功能数据,分页显示
     */
    @PostMapping("/queryFunctionDatas")
    public CommonResult<CommonPage<Map<String, Object>>> queryFunctionDatas(@RequestBody @Valid FunctionDatas pojo) {
        CommonPage<Map<String, Object>> commonPage = functionDatasService.queryFunctionDatasPages(pojo.getPhone(), pojo.getStartDate(), pojo.getEndDate(), pojo.getPageNum(), pojo.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 导出功能数据 Excel
     */
    @PostMapping("/exportFunctionDatasExcel")
    public void exportFunctionDatasExcel(HttpServletResponse response,@RequestBody @Valid FunctionDatas param) {
        try {
            // 获取累计交易数据
            List<Map<String, Object>> dataList = functionDatasService.exportFunctionDatasExcel(param.getPhone(),param.getStartDate(), param.getEndDate());
            String todayTotalFunctionDatas = functionDatasService.todayTotalFunctionDatas(param.getPhone(),param.getStartDate());   //今日总功能数值
            String todayTotalQuantify = functionDatasService.todayTotalQuantify(param.getPhone(),param.getStartDate());             //今日总量化值
            String todayTotalSubsidy = functionDatasService.todayTotalSubsidy(param.getPhone(),param.getStartDate());                 //今日总补贴金
            String todayAllTotalFunctionDatas = functionDatasService.todayAllTotalFunctionDatas(param.getPhone(),param.getStartDate()); //今日总累计功能数值
            //今日总累计量化值
            String todayAllTotalQuantify = functionDatasService.todayAllTotalQuantify(param.getPhone(),param.getStartDate());
            //今日总累计补贴金
            String todayAllTotalSubsidy = functionDatasService.todayAllTotalSubsidy(param.getPhone(),param.getStartDate());


            // 配置字段映射（只映射实体类中存在的字段）
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("value", "value");
            fieldMapping.put("quantifyValue", "quantifyValue");
            fieldMapping.put("subsidyFunds", "subsidyFunds");

            // 配置汇总信息（一行显示3个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                // 第一行：3个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总功能数据")
                        .value(todayTotalFunctionDatas)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总量化数")
                        .value(todayTotalQuantify)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总补贴金")
                        .value(todayTotalSubsidy)
                        .build()
                ),
                // 第二行：3个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总累计功能数值")
                        .value(todayAllTotalFunctionDatas)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总累计量化值")
                        .value(todayAllTotalQuantify)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总累计补贴金")
                        .value(todayAllTotalSubsidy)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<FunctionDatas> config = UniversalExcelExporter.ExportConfig.<FunctionDatas>builder()
                    .dataList(dataList)
                    .entityClass(FunctionDatas.class)
                    .fileName("功能数据")
                    .sheetName("功能数据")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出功能数据异常", e);
        }
    }
}
