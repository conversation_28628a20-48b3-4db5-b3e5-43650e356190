package com.mall.project.service.cQuantifyEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * C量化值进化量服务接口
 */
public interface CQuantifyEvolveService {

    /**
     * 计算用户类型为C的量化值进化量
     */
    public void updateCQuantifyEvolve();

    /**
     * 查询C用户量化值进化量, 分页显示
     */
    public CommonPage<Map<String, Object>> queryCQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize, Integer isGreaterThanZero);

    /**
     * C用户量化值进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportCQuantifyEvolveExcel(String phone, String startDate, String endDate);

    /**
     * 今日C量化值进化量
     */
    public String todayCQuantifyEvolve(String phone, String startTime);

    /**
     * 累计C量化值进化量
     */
    public String totalCQuantifyEvolve(String phone, String startTime);
}
