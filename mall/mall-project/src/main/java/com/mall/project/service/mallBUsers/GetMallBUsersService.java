package com.mall.project.service.mallBUsers;

import com.mall.common.api.CommonPage;

import java.util.Map;

public interface GetMallBUsersService {

    // 从mallB系统读取用户数据
    void getMallBUsers();

    // 从mallB系统读取超级管理员手机号码
    void getSuperAdminPhone();

    // 从本地系统读取用户数据
    CommonPage<Map<String, Object>> getMallBUsers(String phone,String businessLicense, int pageNum, int pageSize);

    //更新用户状态
    int updateMallBUsersStatus(String id, String status);
}
