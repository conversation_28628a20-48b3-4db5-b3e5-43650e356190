package com.mall.project.dao.positions;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class PositionsDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<Map<String, Object>> getPositions() {
        // 查询所有状态为有效的岗位
        String sql = "SELECT id, position_name FROM positions WHERE status = 1";
        return jdbcTemplate.queryForList(sql);
    }
    
    /**
     * 获取所有岗位详细信息，用于导出Excel
     */
    public List<Map<String, Object>> getAllPositionsForExport() {
        String sql = "SELECT p.id, p.position_name, p.description, " +
                     "CASE p.status WHEN 1 THEN '有效' WHEN 0 THEN '无效' ELSE '未知' END AS status_text, " +
                     "p.create_time, p.update_time " +
                     "FROM positions p " +
                     "ORDER BY p.id";
        return jdbcTemplate.queryForList(sql);
    }
    public List<Map<String, Object>> getPositionsPaged(String searchName, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, position_name FROM positions WHERE status = 1");

        if (searchName != null && !searchName.trim().isEmpty()) {
            sql.append(" AND position_name LIKE ?");
            params.add("%" + searchName.trim() + "%");
        }
        sql.append(" ORDER BY id LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);

        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    public int countPositions(String searchName) {
        List<Object> params = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM positions WHERE status = 1");

        if (searchName != null && !searchName.trim().isEmpty()) {
            sql.append(" AND position_name LIKE ?");
            params.add("%" + searchName.trim() + "%");
        }

        return jdbcTemplate.queryForObject(sql.toString(), Integer.class, params.toArray());
    }
}
