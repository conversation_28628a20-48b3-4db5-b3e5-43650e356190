package com.mall.project.dto.functionDatas;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 功能数据实体类
 */
@Data
public class FunctionDatas {

    @ExcelProperty("时间")
    private String updateDate;            //更新时间
    @ExcelProperty("手机号")
    private String phone;                 //手机号
    @ExcelProperty("功能数值")
    private String value;                 //功能数值
    @ExcelProperty("量化值")
    private String quantifyValue;         //量化值
    @ExcelProperty("补贴金")
    private String subsidyFunds;          //补贴金

    @ExcelIgnore  //Excel导出时忽略此字段
    private String enterpriseId;          //企业ID

    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;             //开始日期
    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;               //结束日期

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
