package com.mall.project.dao.operationPassword;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
@Slf4j
public class OperationPasswordDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;


    /**
     * 获取操作密码
     */
    public Map<String, Object> getOperationPassword(String phone) {
        String sql = "SELECT phone FROM operation_password WHERE phone = ?";
        try {
            return jdbcTemplate.queryForMap(sql, phone);
        } catch (Exception e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }

    /**
     * 更新或修改操作密码
     */
    public int saveOrUpdateOperationPassword(String password, String phone, Integer updatePerson) {
        try {
            //如果phone 不存在则插入数据,存在则更新数据 select exist phone from operation_password where phone = ?
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM operation_password WHERE phone = ?)", Integer.class, phone) == 0) {
                String sql = "INSERT INTO operation_password(password, phone, update_person, update_time) VALUES (?, ?, ?, NOW())";
                return jdbcTemplate.update(sql, password, phone, updatePerson);
            }
            String sql = "UPDATE operation_password SET password = ?, update_person = ?, update_time = NOW() WHERE phone = ?";
            return jdbcTemplate.update(sql, password, updatePerson, phone);
        } catch (Exception e) {
            log.error("更新操作密码失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 验证操作密码
     */
    public boolean verifyOperationPassword(String password, String phone) {
        try {
            String sql = "SELECT password FROM operation_password WHERE phone = ?";
            String dbPassword = jdbcTemplate.queryForObject(sql, String.class, phone);
            return password.equals(dbPassword);
        } catch (org.springframework.dao.EmptyResultDataAccessException e) {
            // 当查询不到记录时，返回false表示验证失败
            return false;
        } catch (Exception e) {
            log.error("验证操作密码失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
